// DIAGNÓSTICO CARRUSEL MÓVIL - PUPPETEER
// Análisis detallado del comportamiento del carrusel en dispositivos móviles

const puppeteer = require('puppeteer');
const fs = require('fs');

// Configuraciones de dispositivos móviles
const mobileDevices = [
    {
        name: 'iPhone 12',
        viewport: { width: 390, height: 844, isMobile: true, hasTouch: true }
    },
    {
        name: 'Samsung Galaxy S21',
        viewport: { width: 384, height: 854, isMobile: true, hasTouch: true }
    },
    {
        name: 'iPhone SE',
        viewport: { width: 375, height: 667, isMobile: true, hasTouch: true }
    }
];

async function diagnosticarCarruselMovil() {
    console.log('🚀 Iniciando diagnóstico del carrusel móvil...');
    
    const browser = await puppeteer.launch({ 
        headless: false, // Cambiar a true para ejecución sin interfaz
        devtools: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const resultados = [];
    
    for (const device of mobileDevices) {
        console.log(`\n📱 Probando en ${device.name}...`);
        
        const page = await browser.newPage();
        await page.setViewport(device.viewport);
        
        // Capturar logs de consola
        const consoleLogs = [];
        page.on('console', msg => {
            if (msg.text().includes('DIAGNÓSTICO MÓVIL') || msg.text().includes('📱')) {
                consoleLogs.push({
                    type: msg.type(),
                    text: msg.text(),
                    timestamp: new Date().toISOString()
                });
            }
        });
        
        try {
            // Navegar a la página (cambiar URL según tu configuración)
            await page.goto('http://localhost:3000', { 
                waitUntil: 'networkidle2',
                timeout: 30000 
            });
            
            // Esperar a que el carrusel se cargue
            await page.waitForSelector('.stepless-ratio-carousel', { timeout: 10000 });
            
            // DIAGNÓSTICO 1: Estado inicial del carrusel
            const estadoInicial = await page.evaluate(() => {
                const carousel = document.querySelector('.stepless-ratio-carousel');
                const swiperContainer = carousel?.querySelector('.swiper');
                const swiper = swiperContainer?.swiper;
                
                if (!swiper) {
                    return { error: 'Swiper no inicializado' };
                }
                
                const wrapper = swiperContainer.querySelector('.swiper-wrapper');
                const slides = swiperContainer.querySelectorAll('.swiper-slide');
                
                return {
                    dispositivo: window.innerWidth + 'x' + window.innerHeight,
                    swiperParams: {
                        slidesPerView: swiper.params.slidesPerView,
                        centeredSlides: swiper.params.centeredSlides,
                        spaceBetween: swiper.params.spaceBetween,
                        loop: swiper.params.loop
                    },
                    estado: {
                        activeIndex: swiper.activeIndex,
                        slidesLength: slides.length,
                        translate: swiper.translate,
                        isBeginning: swiper.isBeginning,
                        isEnd: swiper.isEnd
                    },
                    estilos: {
                        wrapperTransform: getComputedStyle(wrapper).transform,
                        wrapperTransition: getComputedStyle(wrapper).transition,
                        wrapperMaxHeight: getComputedStyle(wrapper).maxHeight
                    }
                };
            });
            
            console.log(`📊 Estado inicial en ${device.name}:`, estadoInicial);
            
            // DIAGNÓSTICO 2: Capturar screenshot inicial
            await page.screenshot({
                path: `diagnostics/screenshots/${device.name.replace(/\s+/g, '-')}-inicial.png`,
                fullPage: false
            });
            
            // DIAGNÓSTICO 3: Simular interacción táctil
            const carouselSelector = '.stepless-ratio-carousel .swiper';
            const carouselElement = await page.$(carouselSelector);
            
            if (carouselElement) {
                const boundingBox = await carouselElement.boundingBox();
                
                // Simular swipe hacia la izquierda
                const startX = boundingBox.x + boundingBox.width * 0.8;
                const endX = boundingBox.x + boundingBox.width * 0.2;
                const y = boundingBox.y + boundingBox.height / 2;
                
                await page.touchscreen.tap(startX, y);
                await page.waitForTimeout(100);
                
                // Realizar swipe
                await page.mouse.move(startX, y);
                await page.mouse.down();
                await page.mouse.move(endX, y, { steps: 10 });
                await page.mouse.up();
                
                // Esperar a que termine la animación
                await page.waitForTimeout(1000);
                
                // DIAGNÓSTICO 4: Estado después del swipe
                const estadoDespuesSwipe = await page.evaluate(() => {
                    const carousel = document.querySelector('.stepless-ratio-carousel');
                    const swiper = carousel?.querySelector('.swiper')?.swiper;
                    
                    if (!swiper) return { error: 'Swiper no encontrado' };
                    
                    return {
                        activeIndex: swiper.activeIndex,
                        translate: swiper.translate,
                        isBeginning: swiper.isBeginning,
                        isEnd: swiper.isEnd
                    };
                });
                
                console.log(`📊 Después del swipe en ${device.name}:`, estadoDespuesSwipe);
                
                // Capturar screenshot después del swipe
                await page.screenshot({
                    path: `diagnostics/screenshots/${device.name.replace(/\s+/g, '-')}-despues-swipe.png`,
                    fullPage: false
                });
            }
            
            // DIAGNÓSTICO 5: Análisis de rendimiento
            const metricas = await page.metrics();
            
            // DIAGNÓSTICO 6: Detectar "brinquito" visual
            const analisisBrinquito = await page.evaluate(() => {
                return new Promise((resolve) => {
                    const carousel = document.querySelector('.stepless-ratio-carousel');
                    const wrapper = carousel?.querySelector('.swiper-wrapper');
                    
                    if (!wrapper) {
                        resolve({ error: 'Wrapper no encontrado' });
                        return;
                    }
                    
                    const posiciones = [];
                    let contador = 0;
                    
                    const observer = new MutationObserver(() => {
                        const transform = getComputedStyle(wrapper).transform;
                        posiciones.push({
                            transform,
                            timestamp: Date.now()
                        });
                        
                        contador++;
                        if (contador >= 5) {
                            observer.disconnect();
                            
                            // Analizar si hay cambios bruscos
                            const hayBrinquito = posiciones.some((pos, index) => {
                                if (index === 0) return false;
                                return pos.transform !== posiciones[index - 1].transform;
                            });
                            
                            resolve({
                                hayBrinquito,
                                posiciones,
                                analisis: 'Cambios detectados en transform'
                            });
                        }
                    });
                    
                    observer.observe(wrapper, {
                        attributes: true,
                        attributeFilter: ['style']
                    });
                    
                    // Timeout de seguridad
                    setTimeout(() => {
                        observer.disconnect();
                        resolve({
                            hayBrinquito: false,
                            posiciones,
                            analisis: 'No se detectaron cambios significativos'
                        });
                    }, 2000);
                });
            });
            
            console.log(`🔍 Análisis de brinquito en ${device.name}:`, analisisBrinquito);
            
            // Compilar resultados para este dispositivo
            const resultado = {
                dispositivo: device.name,
                viewport: device.viewport,
                estadoInicial,
                estadoDespuesSwipe: estadoDespuesSwipe || null,
                analisisBrinquito,
                metricas: {
                    JSHeapUsedSize: metricas.JSHeapUsedSize,
                    JSHeapTotalSize: metricas.JSHeapTotalSize,
                    ScriptDuration: metricas.ScriptDuration
                },
                consoleLogs
            };
            
            resultados.push(resultado);
            
        } catch (error) {
            console.error(`❌ Error en ${device.name}:`, error.message);
            resultados.push({
                dispositivo: device.name,
                error: error.message
            });
        }
        
        await page.close();
    }
    
    await browser.close();
    
    // Guardar resultados en archivo JSON
    const reporteFinal = {
        timestamp: new Date().toISOString(),
        resumen: {
            dispositivosProbados: mobileDevices.length,
            problemasDetectados: resultados.filter(r => r.analisisBrinquito?.hayBrinquito).length
        },
        resultados
    };
    
    fs.writeFileSync(
        'diagnostics/reporte-carrusel-movil.json', 
        JSON.stringify(reporteFinal, null, 2)
    );
    
    console.log('\n📋 RESUMEN DEL DIAGNÓSTICO:');
    console.log(`✅ Dispositivos probados: ${mobileDevices.length}`);
    console.log(`⚠️ Problemas detectados: ${reporteFinal.resumen.problemasDetectados}`);
    console.log('📄 Reporte completo guardado en: diagnostics/reporte-carrusel-movil.json');
    
    return reporteFinal;
}

// Ejecutar diagnóstico si se llama directamente
if (require.main === module) {
    diagnosticarCarruselMovil()
        .then(reporte => {
            console.log('🎉 Diagnóstico completado exitosamente');
        })
        .catch(error => {
            console.error('❌ Error en el diagnóstico:', error);
        });
}

module.exports = { diagnosticarCarruselMovil };
