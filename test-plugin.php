<?php
/**
 * Script de prueba para validar el plugin <PERSON><PERSON> Carousel Enhancer
 * Este script simula un entorno básico de WordPress para probar el plugin
 */

// Simular constantes básicas de WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

if (!defined('WP_PLUGIN_DIR')) {
    define('WP_PLUGIN_DIR', __DIR__ . '/plugins/');
}

// Simular funciones básicas de WordPress que el plugin podría usar
function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
    echo "✓ Hook registrado: $hook\n";
    return true;
}

function add_filter($hook, $callback, $priority = 10, $accepted_args = 1) {
    echo "✓ Filter registrado: $hook\n";
    return true;
}

function plugin_dir_path($file) {
    return dirname($file) . '/';
}

function plugin_dir_url($file) {
    return 'http://localhost/' . basename(dirname($file)) . '/';
}

function wp_register_script($handle, $src, $deps = array(), $ver = false, $in_footer = false) {
    echo "✓ Script registrado: $handle\n";
    return true;
}

function wp_register_style($handle, $src, $deps = array(), $ver = false, $media = 'all') {
    echo "✓ Style registrado: $handle\n";
    return true;
}

function wp_enqueue_script($handle) {
    echo "✓ Script encolado: $handle\n";
}

function wp_enqueue_style($handle) {
    echo "✓ Style encolado: $handle\n";
}

function wp_localize_script($handle, $name, $data) {
    echo "✓ Script localizado: $handle\n";
}

function wp_add_inline_style($handle, $data) {
    echo "✓ CSS inline añadido a: $handle\n";
}

function get_option($option, $default = false) {
    // Simular opciones por defecto del plugin
    $defaults = array(
        'desktop_height' => '55vh',
        'tablet_height' => '40vh',
        'mobile_height' => '35vh',
        'slide_spacing' => '0px',
        'mobile_spacing' => '0px',
        'tablet_spacing' => '0px',
        'negative_spacing' => '0px',
        'carousel_background' => 'transparent',
        'initial_delay' => '300',
        'fade_delay' => '100',
        'error_opacity' => '0.5',
        'enable_lazy_loading' => 'yes',
        'enable_hover_effect' => 'no',
        'enable_shadow' => 'no'
    );
    return $defaults;
}

function add_options_page($page_title, $menu_title, $capability, $menu_slug, $callback) {
    echo "✓ Página de opciones añadida: $menu_title\n";
}

function register_setting($option_group, $option_name, $args = array()) {
    echo "✓ Setting registrado: $option_name\n";
}

function add_settings_section($id, $title, $callback, $page) {
    echo "✓ Sección de settings añadida: $id\n";
}

function add_settings_field($id, $title, $callback, $page, $section, $args = array()) {
    echo "✓ Campo de setting añadido: $id\n";
}

function load_plugin_textdomain($domain, $deprecated = false, $plugin_rel_path = false) {
    echo "✓ Textdomain cargado: $domain\n";
}

function plugin_basename($file) {
    return basename($file);
}

function wp_script_is($handle, $list = 'enqueued') {
    return false; // Simular que no hay scripts cargados
}

function did_action($hook) {
    return false; // Simular que no se han ejecutado acciones
}

function esc_attr($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

function esc_html($text) {
    return htmlspecialchars($text, ENT_NOQUOTES, 'UTF-8');
}

function sanitize_text_field($str) {
    return trim(strip_tags($str));
}

function intval($var) {
    return (int) $var;
}

function floatval($var) {
    return (float) $var;
}

function __($text, $domain = 'default') {
    return $text;
}

function _e($text, $domain = 'default') {
    echo $text;
}

function checked($checked, $current = true, $echo = true) {
    $result = checked($checked, $current, false);
    if ($echo) {
        echo $result;
    }
    return $result;
}

// Simular clase de Elementor
if (!class_exists('\Elementor\Frontend')) {
    class DummyElementorFrontend {
        // Clase dummy para evitar errores
    }
    class_alias('DummyElementorFrontend', '\Elementor\Frontend');
}

echo "=== INICIANDO PRUEBA DEL PLUGIN MADAM CAROUSEL ENHANCER ===\n\n";

try {
    // Intentar cargar el plugin
    require_once './plugins/madam-carousel-enhancer/madam-carousel-enhancer.php';
    
    echo "\n✅ ÉXITO: El plugin se cargó sin errores fatales!\n";
    echo "✓ La clase Madam_Carousel_Enhancer fue definida correctamente\n";
    
    // Verificar que la instancia se puede crear
    if (class_exists('Madam_Carousel_Enhancer')) {
        echo "✓ La clase existe y se puede instanciar\n";
    } else {
        echo "❌ ERROR: La clase no fue definida correctamente\n";
    }
    
} catch (ParseError $e) {
    echo "❌ ERROR DE SINTAXIS PHP:\n";
    echo "Archivo: " . $e->getFile() . "\n";
    echo "Línea: " . $e->getLine() . "\n";
    echo "Mensaje: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ ERROR FATAL:\n";
    echo "Archivo: " . $e->getFile() . "\n";
    echo "Línea: " . $e->getLine() . "\n";
    echo "Mensaje: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ EXCEPCIÓN:\n";
    echo "Archivo: " . $e->getFile() . "\n";
    echo "Línea: " . $e->getLine() . "\n";
    echo "Mensaje: " . $e->getMessage() . "\n";
}

echo "\n=== FIN DE LA PRUEBA ===\n";
?>
