// DIAGNÓSTICO CARRUSEL MÓVIL - PLAYWRIGHT
// Prueba específica para detectar el problema del "brinquito" en móvil

const { test, expect, devices } = require('@playwright/test');

// Configuración de dispositivos móviles para probar
const mobileDevices = [
    devices['iPhone 12'],
    devices['iPhone SE'],
    devices['Samsung Galaxy S21'],
    devices['Pixel 5']
];

test.describe('Diagnóstico Carrusel Móvil', () => {
    
    mobileDevices.forEach(device => {
        test(`Carrusel en ${device.name}`, async ({ browser }) => {
            const context = await browser.newContext({
                ...device,
                // Habilitar logs de consola
                recordVideo: { dir: 'test-results/videos/' }
            });
            
            const page = await context.newPage();
            
            // Capturar logs de consola para diagnóstico
            const consoleLogs = [];
            page.on('console', msg => {
                if (msg.text().includes('DIAGNÓSTICO MÓVIL')) {
                    consoleLogs.push(msg.text());
                }
            });
            
            // Navegar a la página con el carrusel
            await page.goto('http://localhost:3000'); // Cambiar por tu URL
            
            // Esperar a que el carrusel se inicialice
            await page.waitForSelector('.stepless-ratio-carousel', { timeout: 10000 });
            
            // DIAGNÓSTICO 1: Verificar estado inicial
            const initialState = await page.evaluate(() => {
                const carousel = document.querySelector('.stepless-ratio-carousel');
                const swiper = carousel?.querySelector('.swiper')?.swiper;
                
                if (!swiper) return { error: 'Swiper no encontrado' };
                
                return {
                    activeIndex: swiper.activeIndex,
                    slidesLength: swiper.slides.length,
                    translate: swiper.translate,
                    isBeginning: swiper.isBeginning,
                    slidesPerView: swiper.params.slidesPerView,
                    centeredSlides: swiper.params.centeredSlides,
                    wrapperTransform: getComputedStyle(swiper.wrapperEl).transform
                };
            });
            
            console.log(`📱 ${device.name} - Estado inicial:`, initialState);
            
            // DIAGNÓSTICO 2: Capturar screenshot inicial
            await page.screenshot({ 
                path: `test-results/screenshots/${device.name.replace(/\s+/g, '-')}-inicial.png`,
                fullPage: false
            });
            
            // DIAGNÓSTICO 3: Simular swipe y verificar comportamiento
            const carouselElement = await page.locator('.stepless-ratio-carousel .swiper');
            
            // Realizar swipe hacia la izquierda
            await carouselElement.hover();
            await page.mouse.down();
            await page.mouse.move(100, 0); // Mover 100px a la derecha
            await page.mouse.up();
            
            // Esperar a que termine la transición
            await page.waitForTimeout(1000);
            
            // DIAGNÓSTICO 4: Verificar estado después del swipe
            const afterSwipeState = await page.evaluate(() => {
                const carousel = document.querySelector('.stepless-ratio-carousel');
                const swiper = carousel?.querySelector('.swiper')?.swiper;
                
                return {
                    activeIndex: swiper.activeIndex,
                    translate: swiper.translate,
                    wrapperTransform: getComputedStyle(swiper.wrapperEl).transform
                };
            });
            
            console.log(`📱 ${device.name} - Después del swipe:`, afterSwipeState);
            
            // Capturar screenshot después del swipe
            await page.screenshot({ 
                path: `test-results/screenshots/${device.name.replace(/\s+/g, '-')}-despues-swipe.png`,
                fullPage: false
            });
            
            // DIAGNÓSTICO 5: Verificar si hay "brinquito" visual
            const hasVisualJump = await page.evaluate(() => {
                const carousel = document.querySelector('.stepless-ratio-carousel');
                const wrapper = carousel?.querySelector('.swiper-wrapper');
                
                if (!wrapper) return false;
                
                // Verificar si hay transiciones bruscas o transforms inconsistentes
                const computedStyle = getComputedStyle(wrapper);
                const transform = computedStyle.transform;
                const transition = computedStyle.transition;
                
                return {
                    transform,
                    transition,
                    hasTransform: transform !== 'none',
                    hasTransition: transition !== 'none'
                };
            });
            
            console.log(`📱 ${device.name} - Análisis visual:`, hasVisualJump);
            
            // DIAGNÓSTICO 6: Verificar CSS responsive
            const responsiveStyles = await page.evaluate(() => {
                const carousel = document.querySelector('.stepless-ratio-carousel');
                const wrapper = carousel?.querySelector('.swiper-wrapper');
                const slide = carousel?.querySelector('.swiper-slide');
                
                if (!carousel || !wrapper || !slide) return null;
                
                const carouselStyles = getComputedStyle(carousel);
                const wrapperStyles = getComputedStyle(wrapper);
                const slideStyles = getComputedStyle(slide);
                
                return {
                    carousel: {
                        width: carouselStyles.width,
                        height: carouselStyles.height
                    },
                    wrapper: {
                        maxHeight: wrapperStyles.maxHeight,
                        transform: wrapperStyles.transform,
                        alignItems: wrapperStyles.alignItems
                    },
                    slide: {
                        width: slideStyles.width,
                        height: slideStyles.height,
                        flex: slideStyles.flex
                    }
                };
            });
            
            console.log(`📱 ${device.name} - Estilos responsive:`, responsiveStyles);
            
            // Guardar logs de consola
            console.log(`📱 ${device.name} - Logs de diagnóstico:`, consoleLogs);
            
            await context.close();
        });
    });
    
    // Test específico para detectar el problema del "brinquito"
    test('Detectar brinquito en carrusel móvil', async ({ browser }) => {
        const context = await browser.newContext(devices['iPhone 12']);
        const page = await context.newPage();
        
        await page.goto('http://localhost:3000'); // Cambiar por tu URL
        await page.waitForSelector('.stepless-ratio-carousel');
        
        // Grabar el comportamiento inicial
        const initialPositions = [];
        
        for (let i = 0; i < 5; i++) {
            const position = await page.evaluate(() => {
                const wrapper = document.querySelector('.stepless-ratio-carousel .swiper-wrapper');
                return {
                    transform: getComputedStyle(wrapper).transform,
                    timestamp: Date.now()
                };
            });
            initialPositions.push(position);
            await page.waitForTimeout(100);
        }
        
        console.log('📊 Posiciones iniciales:', initialPositions);
        
        // Verificar si hay movimiento inesperado
        const hasUnexpectedMovement = initialPositions.some((pos, index) => {
            if (index === 0) return false;
            return pos.transform !== initialPositions[0].transform;
        });
        
        if (hasUnexpectedMovement) {
            console.log('⚠️ PROBLEMA DETECTADO: Movimiento inesperado en la inicialización');
        }
        
        await context.close();
    });
});
