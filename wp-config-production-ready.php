<?php

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the web site, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * Localized language
 * * ABSPATH
 *
 * @link https://wordpress.org/support/article/editing-wp-config-php/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'u506901340_Ewh6y' );

/** Database username */
define( 'DB_USER', 'u506901340_tUcEu' );

/** Database password */
define( 'DB_PASSWORD', 'nlASNWC77Z' );

/** Database hostname */
define( 'DB_HOST', '127.0.0.1' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',          'kq$XYma.0@V.+Scu=vdiVSpCUrAfUKp,ZAi_,%.Xp+Fg;*ZWI|xr;`H!8O+ZU66d' );
define( 'SECURE_AUTH_KEY',   '<9DD  @i[*AyzBWV:U{b!f4]vvh3aC4rmo71udDxO.Y28.<DTwf/g!&]l<UdYl>.' );
define( 'LOGGED_IN_KEY',     'YpApJ[m~Y3{q%7|n@=04ed:)(cGEcP880iWyZR]-d&[f?U|^dU+X;F38LJae$]|g' );
define( 'NONCE_KEY',         'm1:%.!LDt/Tx9U~(g|ie0|`lwHs(6!Bww9e{-2TQLHKqg`yM@!9 n[}Fq#3I0_P!' );
define( 'AUTH_SALT',         'n;KWA^7i>S@fbNWauTu7x5Pa!oKb_*hOATN9fREm}=c,?1 ui)}c_(XxpAWFkv; ' );
define( 'SECURE_AUTH_SALT',  '*W$}bP2VNlrN`XF=!quXf+B>?>$lbi!?[#,mr[iYp_ZXM4PQ8C5RcX70&p`jy:80' );
define( 'LOGGED_IN_SALT',    'aa:fD3ns6(C(Fjs]M)_<rv!j)5`dnG20f+^$Eu30D9%H~Q/Q%sU:Vh:+RonT|!@O' );
define( 'NONCE_SALT',        'q]9a`U8t3s4un)PS?LQ5,AXv~DXJKrAg^~n&PPzw;pN+72{9VI_x(BLr9gJ={iHo' );
define( 'WP_CACHE_KEY_SALT', 'jO(>L}I`]C}D^*L:&S[M1B?3AD#~vj8h:FNO;4S!jwNyjDfYoc-^c,u&DS4Y[-+s' );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'wp_';

/* Add any custom values between this line and the "stop editing" line. */

/**
 * ===================================================================
 * ENHANCED DEBUG CONFIGURATION FOR MADAM CAROUSEL ENHANCER PLUGIN
 * ===================================================================
 * 
 * These debug settings are specifically configured to help test and
 * debug the Madam Carousel Enhancer plugin activation and functionality.
 * 
 * IMPORTANT: Set WP_DEBUG to false in production!
 */

// Enable WordPress debug mode (ESSENTIAL for plugin testing)
if ( ! defined( 'WP_DEBUG' ) ) {
	define( 'WP_DEBUG', true );
}

// Log all errors to wp-content/debug.log (check this file for errors)
define( 'WP_DEBUG_LOG', true );

// Don't display errors on screen (security - logs only)
define( 'WP_DEBUG_DISPLAY', false );

// Use uncompressed versions of CSS and JS files for debugging
define( 'SCRIPT_DEBUG', true );

// Disable the fatal error handler to see full error details
define( 'WP_DISABLE_FATAL_ERROR_HANDLER', true );

// Increase memory limit for plugin operations
define( 'WP_MEMORY_LIMIT', '512M' );
ini_set( 'memory_limit', '512M' );

// Set comprehensive error reporting
ini_set( 'error_reporting', E_ALL );
ini_set( 'display_errors', 0 );
ini_set( 'log_errors', 1 );

// Enable query debugging (optional - helps with database issues)
// Uncomment the line below if you need to debug database queries
// define( 'SAVEQUERIES', true );

/**
 * Additional WordPress configuration
 */
define( 'FS_METHOD', 'direct' );
define( 'COOKIEHASH', 'a13edc90d1afa731b36e7771de51093f' );
define( 'WP_AUTO_UPDATE_CORE', 'minor' );

/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
