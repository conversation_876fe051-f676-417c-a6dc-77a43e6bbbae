﻿param(
    [string]$InputDir,
    [string]$OutputDir,
    [int]$MaxSize = 2560,
    [int]$Quality = 80,
    [int]$Effort = 3
)

# Función para mostrar ayuda
function Show-Help {
    Write-Host "Convertidor de JPG a AVIF" -ForegroundColor Green
    Write-Host "Uso: .\Convert-JPGToAVIF.ps1 -InputDir <ruta> -OutputDir <ruta> [-MaxSize <numero>] [-Quality <numero>] [-Effort <numero>]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Parámetros:"
    Write-Host "  -InputDir   : Directorio con archivos JPG"
    Write-Host "  -OutputDir  : Directorio de salida para archivos AVIF"
    Write-Host "  -MaxSize    : Tamaño máximo en píxeles (default: 2560)"
    Write-Host "  -Quality    : Calidad 1-100 (default: 80)"
    Write-Host "  -Effort     : Esfuerzo de compresión 0-9 (default: 3)"
    exit 0
}

# Verificar si se pidió ayuda
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "-h") {
    Show-Help
}

# Función para verificar dependencias
function Test-Dependencies {
    Write-Host "🔍 Verificando dependencias..." -ForegroundColor Yellow
    
    # Verificar FFmpeg
    try {
        $ffmpeg = ffmpeg -version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ FFmpeg encontrado" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ FFmpeg no encontrado. Se requiere FFmpeg para procesar las imágenes." -ForegroundColor Red
        Write-Host "💡 Instalar desde: https://ffmpeg.org/download.html" -ForegroundColor Yellow
        exit 1
    }
}

# Función para obtener dimensiones de imagen
function Get-ImageDimensions {
    param([string]$ImagePath)
    
    try {
        $info = ffprobe -v quiet -print_format json -show_streams "$ImagePath" | ConvertFrom-Json
        $videoStream = $info.streams | Where-Object { $_.codec_type -eq "video" } | Select-Object -First 1
        
        return @{
            Width = [int]$videoStream.width
            Height = [int]$videoStream.height
            IsValid = $true
        }
    } catch {
        Write-Warning "No se pudieron obtener dimensiones de: $ImagePath"
        return @{ IsValid = $false }
    }
}

# Función para calcular dimensiones de resize
function Get-ResizeDimensions {
    param(
        [int]$OriginalWidth,
        [int]$OriginalHeight,
        [int]$MaxSize
    )
    
    $maxDimension = [Math]::Max($OriginalWidth, $OriginalHeight)
    
    if ($maxDimension -le $MaxSize) {
        # La imagen ya es menor o igual al tamaño máximo
        return @{
            Width = $OriginalWidth
            Height = $OriginalHeight
            NeedsResize = $false
        }
    }
    
    # Calcular nueva escala manteniendo relación de aspecto
    $scale = $MaxSize / $maxDimension
    $newWidth = [Math]::Round($OriginalWidth * $scale)
    $newHeight = [Math]::Round($OriginalHeight * $scale)
    
    return @{
        Width = $newWidth
        Height = $newHeight
        NeedsResize = $true
    }
}

# Función principal
function Convert-Images {
    Write-Host "🚀 Iniciando conversión de JPG a AVIF..." -ForegroundColor Green
    Write-Host "📁 Entrada: $InputDir" -ForegroundColor Cyan
    Write-Host "📁 Salida: $OutputDir" -ForegroundColor Cyan
    Write-Host "📏 Tamaño máximo: $MaxSize px" -ForegroundColor Cyan
    Write-Host "🎯 Calidad: $Quality" -ForegroundColor Cyan
    Write-Host "⚡ Esfuerzo: $Effort" -ForegroundColor Cyan
    Write-Host ""
    
    # Verificar directorios
    if (!(Test-Path $InputDir)) {
        Write-Host "❌ El directorio de entrada no existe: $InputDir" -ForegroundColor Red
        exit 1
    }
    
    if (!(Test-Path $OutputDir)) {
        Write-Host "📁 Creando directorio de salida: $OutputDir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    }
    
    # Buscar archivos JPG
    $jpgFiles = Get-ChildItem -Path $InputDir -Filter "*.jpg" -File
    
    if ($jpgFiles.Count -eq 0) {
        Write-Host "⚠️  No se encontraron archivos JPG en: $InputDir" -ForegroundColor Yellow
        exit 0
    }
      Write-Host "📸 Encontrados $($jpgFiles.Count) archivos JPG" -ForegroundColor Green
    Write-Host ""
    
    # Contadores
    $processed = 0
    $failed = 0
    $totalSize = 0
    $compressedSize = 0
    
    foreach ($file in $jpgFiles) {
        $processed++
        $inputPath = $file.FullName
        $outputFileName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name) + ".avif"
        $outputPath = Join-Path $OutputDir $outputFileName
        
        Write-Host "[$processed/$($jpgFiles.Count)] Procesando: $($file.Name)" -ForegroundColor Yellow
        
        # Obtener dimensiones originales
        $dimensions = Get-ImageDimensions -ImagePath $inputPath
        if (!$dimensions.IsValid) {
            $failed++
            continue
        }
        
        # Calcular nuevas dimensiones
        $newDims = Get-ResizeDimensions -OriginalWidth $dimensions.Width -OriginalHeight $dimensions.Height -MaxSize $MaxSize
          Write-Host "  📐 Original: $($dimensions.Width)x$($dimensions.Height)" -ForegroundColor Gray
        Write-Host "  📐 Nuevo: $($newDims.Width)x$($newDims.Height)" -ForegroundColor Gray
        
        # Calcular cqLevel basado en Quality (80 quality ≈ cqLevel 23)
        $cqLevel = [Math]::Round(51 - ($Quality * 0.51))
        
        try {
            # Usar FFmpeg en lugar de squoosh-cli para mayor compatibilidad
            $outputPath = Join-Path $OutputDir $outputFileName
            
            if ($newDims.NeedsResize) {
                # Con resize usando FFmpeg
                $result = ffmpeg -i "$inputPath" -vf "scale=$($newDims.Width):$($newDims.Height)" -c:v libaom-av1 -crf $cqLevel -cpu-used $Effort "$outputPath" -y 2>&1
            } else {
                # Sin resize usando FFmpeg
                $result = ffmpeg -i "$inputPath" -c:v libaom-av1 -crf $cqLevel -cpu-used $Effort "$outputPath" -y 2>&1
            }
            
            if ($LASTEXITCODE -eq 0) {
                # Calcular estadísticas de compresión
                $originalSize = (Get-Item $inputPath).Length
                $compressedSize += (Get-Item $outputPath).Length
                $totalSize += $originalSize
                
                $compressionRatio = [Math]::Round(((Get-Item $outputPath).Length / $originalSize) * 100, 2)
                Write-Host "  ✅ Completado - Compresión: $compressionRatio%" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Error en conversión" -ForegroundColor Red
                Write-Host "  $result" -ForegroundColor Red
                $failed++
            }
        } catch {
            Write-Host "  ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
            $failed++
        }
        
        Write-Host ""
    }
    
    # Resumen final
    Write-Host "🎉 ¡Conversión completada!" -ForegroundColor Green
    Write-Host "✅ Procesados exitosamente: $($processed - $failed)" -ForegroundColor Green
    Write-Host "❌ Fallidos: $failed" -ForegroundColor Red
    
    if ($totalSize -gt 0) {
        $totalCompressionRatio = [Math]::Round(($compressedSize / $totalSize) * 100, 2)
        $savedSpace = [Math]::Round(($totalSize - $compressedSize) / 1MB, 2)
        Write-Host "💾 Compresión total: $totalCompressionRatio%" -ForegroundColor Cyan
        Write-Host "💾 Espacio ahorrado: $savedSpace MB" -ForegroundColor Cyan
    }
}

# Verificar dependencias y ejecutar
Test-Dependencies
Convert-Images
