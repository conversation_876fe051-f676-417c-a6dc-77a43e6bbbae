<!-- 
CAROUSEL STEPLESS RESPONSIVO - PRODUCCIÓN
Compatible con Elementor Pro 3.29.0 + <PERSON>wiper 8
✅ Desktop: Movimiento stepless continuo
✅ Tablet/Móvil: Comportamiento normal de Elementor
-->

<style>
/* CAROUSEL STEPLESS RESPONSIVO - CSS OPTIMIZADO */

.stepless-ratio-carousel .swiper-wrapper {
    max-height: 52vh;
    align-items: center;
    will-change: transform;
    transform: translate3d(0, 0, 0);
}

/* CONFIGURACIÓN ESPECÍFICA PARA DESKTOP (1025px+) */
@media (min-width: 1025px) {
    .stepless-ratio-carousel.desktop-stepless .swiper-wrapper {
        transition-timing-function: linear !important;
        -webkit-transition-timing-function: linear !important;
        -o-transition-timing-function: linear !important;
    }
}

.stepless-ratio-carousel .swiper-slide {
    width: auto !important;
    height: 100%;
    display: flex;
    align-items: center;
    flex: 0 0 auto !important;
    pointer-events: all !important;
    cursor: pointer;
    position: relative;
    z-index: 2;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
}

.stepless-ratio-carousel .swiper-slide img {
    max-height: 52.5vh;
    width: auto;
    object-fit: contain;  /* Mantiene proporción sin cortar */
    aspect-ratio: attr(width) / attr(height);  /* CSS nativo para proporción */
    transform: translate3d(0, 0, 0);
    pointer-events: all !important;
    cursor: pointer;
    position: relative;
    z-index: 5;
}

/* Enlaces y elementos clickeables */
.stepless-ratio-carousel a,
.stepless-ratio-carousel a img,
.stepless-ratio-carousel .elementor-image,
.stepless-ratio-carousel .elementor-widget-container {
    pointer-events: all !important;
    position: relative;
    z-index: 5;
    cursor: pointer;
}

/* Slides duplicados */
.stepless-ratio-carousel .swiper-slide-duplicate,
.stepless-ratio-carousel .swiper-slide[aria-hidden="true"] {
    pointer-events: all !important;
    cursor: pointer;
    z-index: 2;
}

.stepless-ratio-carousel .swiper-slide-duplicate *,
.stepless-ratio-carousel .swiper-slide[aria-hidden="true"] * {
    pointer-events: all !important;
    z-index: 5;
    cursor: pointer !important;
}

/* RESPONSIVE - TABLET */
@media (max-width: 1024px) and (min-width: 769px) {
    .stepless-ratio-carousel .swiper-wrapper {
        max-height: 45vh;
    }
    .stepless-ratio-carousel .swiper-slide img {
        max-height: 45vh;
    }
}

/* RESPONSIVE - MÓVIL */
@media (max-width: 768px) {
    .stepless-ratio-carousel .swiper-wrapper {
        max-height: 40vh;
    }
    .stepless-ratio-carousel .swiper-slide img {
        max-height: 40vh;
    }
    .stepless-ratio-carousel {
        touch-action: pan-y;
    }
}

@media (max-width: 480px) {
    .stepless-ratio-carousel .swiper-wrapper {
        max-height: 52vh;
    }
    .stepless-ratio-carousel .swiper-slide img {
        max-height: 52vh;
    }
}

/* ACCESIBILIDAD */
@media (prefers-reduced-motion: reduce) {
    .stepless-ratio-carousel .swiper-wrapper {
        animation: none !important;
        transition: none !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {function isDesktopDevice() {
        const screenWidth = window.innerWidth;
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        const userAgent = navigator.userAgent.toLowerCase();
        
        const hasMinWidth = screenWidth >= 1025;
        const isNotMobile = !(/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent));
        const isNotTouch = !isTouchDevice;
        
        return hasMinWidth && (isNotMobile || !isNotTouch);
    }// FUNCIÓN PRINCIPAL
    function initializeSmartCarousels() {
        const carousels = document.querySelectorAll('.stepless-ratio-carousel');
        const isDesktop = isDesktopDevice();
        
        if (carousels.length === 0) {
            setTimeout(initializeSmartCarousels, 1000);
            return;
        }
        
        carousels.forEach(function(carousel, index) {
            if (isDesktop) {
                carousel.classList.add('desktop-stepless');
                setupSteplessCarousel(carousel, index);
            } else {
                carousel.classList.remove('desktop-stepless');
                setupBasicClickability(carousel, index);
            }
        });
        
        observeCarouselChanges(carousels, isDesktop);
    }
      // CONFIGURACIÓN STEPLESS PARA DESKTOP
    function setupSteplessCarousel(carousel, index) {
        const swiperContainer = carousel.querySelector('.swiper');
        
        if (swiperContainer && swiperContainer.swiper) {
            const swiper = swiperContainer.swiper;
            
            swiper.autoplay.stop();
            swiper.autoplay.pause();
            
            Object.assign(swiper.params, {
                autoplay: {
                    delay: 0,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: false,
                    reverseDirection: false,
                    stopOnLastSlide: false
                },                speed: 10000,
                loop: true,
                slidesPerView: 'auto',
                allowTouchMove: true,
                simulateTouch: true,
                touchRatio: 0.1,
                resistance: true,
                resistanceRatio: 0.1,                watchSlidesProgress: true,
                centeredSlides: true,
            });
            
            Object.assign(swiper.params.autoplay, {
                delay: 0,
                disableOnInteraction: false,
                pauseOnMouseEnter: false
            });
            
            const wrapper = swiperContainer.querySelector('.swiper-wrapper');
            if (wrapper) {
                wrapper.style.transitionTimingFunction = 'linear';
                wrapper.style.webkitTransitionTimingFunction = 'linear';
            }
            
            configureSlideClickability(swiperContainer);
            
            setTimeout(function() {
                swiper.autoplay.stop();
                setTimeout(function() {
                    swiper.autoplay.start();
                }, 300);
            }, 500);
            
            setupSteplessMonitor(swiper, index);
        }
    }      // CONFIGURACIÓN BÁSICA PARA MÓVIL/TABLET
    function setupBasicClickability(carousel, index) {
        const swiperContainer = carousel.querySelector('.swiper');
        if (swiperContainer) {
            configureSlideClickability(swiperContainer);

            if (swiperContainer.swiper) {
                const swiper = swiperContainer.swiper;

                // DIAGNÓSTICO: Log del estado inicial
                console.log('🔍 DIAGNÓSTICO MÓVIL - Carrusel:', index);
                console.log('📱 Slides totales:', swiper.slides.length);
                console.log('📱 Slide activo inicial:', swiper.activeIndex);
                console.log('📱 Configuración actual:', {
                    slidesPerView: swiper.params.slidesPerView,
                    centeredSlides: swiper.params.centeredSlides,
                    spaceBetween: swiper.params.spaceBetween
                });

                // Configuración optimizada para móvil
                Object.assign(swiper.params, {
                    centeredSlides: false,  // CAMBIO: Desactivar centrado que puede causar brincos
                    slidesPerView: 1,       // CAMBIO: Mostrar exactamente 1 slide
                    spaceBetween: 10,       // CAMBIO: Pequeño espacio entre slides
                    initialSlide: 0         // NUEVO: Asegurar que inicie en el primer slide
                });

                // Forzar actualización y ir al primer slide
                swiper.update();
                swiper.slideTo(0, 0); // Ir al slide 0 sin animación

                // DIAGNÓSTICO: Log después de la configuración
                setTimeout(() => {
                    console.log('📱 Estado después de configuración:', {
                        activeIndex: swiper.activeIndex,
                        translateX: swiper.translate,
                        isBeginning: swiper.isBeginning
                    });
                }, 100);
            }
        }
    }
      // MONITOR PARA STEPLESS (SOLO DESKTOP)
    function setupSteplessMonitor(swiper, index) {
        let consecutiveFailures = 0;
        const maxFailures = 3;
        
        const smartMonitor = setInterval(function() {
            if (!swiper.destroyed) {
                if (!swiper.autoplay.running && !swiper.autoplay.paused) {
                    consecutiveFailures++;
                    
                    if (consecutiveFailures >= maxFailures) {
                        swiper.autoplay.stop();
                        swiper.update();
                        
                        setTimeout(function() {
                            swiper.autoplay.start();
                            consecutiveFailures = 0;
                        }, 500);
                    }
                } else {
                    consecutiveFailures = 0;
                }
            } else {
                clearInterval(smartMonitor);
            }
        }, 20000);
        
        swiper.on('transitionEnd', function() {
            if (!swiper.autoplay.running && !swiper.destroyed) {
                setTimeout(function() {
                    swiper.autoplay.start();
                }, 100);
            }
        });
    }
      // CONFIGURAR CLICKEABILIDAD (COMÚN PARA TODOS)
    function configureSlideClickability(swiperContainer) {
        const allSlides = swiperContainer.querySelectorAll('.swiper-slide');
        
        allSlides.forEach(function(slide) {
            slide.style.pointerEvents = 'all';
            slide.style.cursor = 'pointer';
            
            const images = slide.querySelectorAll('img');
            images.forEach(function(img) {
                img.style.pointerEvents = 'all';
                img.style.cursor = 'pointer';
                
                if (!img.closest('a')) {
                    const link = document.createElement('a');
                    link.href = img.getAttribute('src') || img.getAttribute('data-src');
                    link.setAttribute('data-elementor-open-lightbox', 'yes');
                    link.setAttribute('data-elementor-lightbox-slideshow', 'gallery');
                    link.style.pointerEvents = 'all';
                    link.style.display = 'block';
                    
                    img.parentNode.insertBefore(link, img);
                    link.appendChild(img);
                }
            });
            
            const links = slide.querySelectorAll('a');
            links.forEach(function(link) {
                link.style.pointerEvents = 'all';
                if (!link.hasAttribute('data-elementor-open-lightbox')) {
                    link.setAttribute('data-elementor-open-lightbox', 'yes');
                    link.setAttribute('data-elementor-lightbox-slideshow', 'gallery');
                }
            });
        });
    }
      // OBSERVER INTELIGENTE
    function observeCarouselChanges(carousels, isDesktop) {
        carousels.forEach(function(carousel) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes.length) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && 
                                (node.classList.contains('swiper-slide') || 
                                 node.classList.contains('swiper-slide-duplicate'))) {
                                
                                node.style.pointerEvents = 'all';
                                node.style.cursor = 'pointer';
                                
                                const newImages = node.querySelectorAll('img:not(a img)');
                                newImages.forEach(function(img) {
                                    img.style.pointerEvents = 'all';
                                    if (!img.closest('a')) {
                                        const link = document.createElement('a');
                                        link.href = img.getAttribute('src');
                                        link.setAttribute('data-elementor-open-lightbox', 'yes');
                                        link.setAttribute('data-elementor-lightbox-slideshow', 'gallery');
                                        img.parentNode.insertBefore(link, img);
                                        link.appendChild(img);
                                    }
                                });
                            }
                        });
                    }
                });
            });
            
            observer.observe(carousel, { 
                childList: true, 
                subtree: true 
            });
        });
    }
      // DETECCIÓN DE CAMBIO DE TAMAÑO
    function handleResize() {
        const wasDesktop = document.querySelector('.stepless-ratio-carousel.desktop-stepless') !== null;
        const isDesktopNow = isDesktopDevice();
        
        if (wasDesktop !== isDesktopNow) {
            setTimeout(function() {
                initializeSmartCarousels();
            }, 500);
        }
    }
    
    // LISTENER PARA CAMBIOS DE TAMAÑO
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResize, 300);
    });
    
    // INICIALIZACIÓN PRINCIPAL
    setTimeout(function() {
        initializeSmartCarousels();
    }, 2500);
});
</script>