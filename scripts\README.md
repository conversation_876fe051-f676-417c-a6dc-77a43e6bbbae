# Scripts de Optimización de Imágenes

Este directorio contiene scripts automatizados para la optimización de imágenes del e-commerce de Madam Supply.

## Convert-JPGToAVIF.ps1

### Descripción

Script de PowerShell que convierte imágenes JPG al formato AVIF (AV1 Image File Format) con redimensionamiento automático y optimización para web. AVIF es un formato de imagen moderno que ofrece una compresión superior comparado con JPEG, WebP y otros formatos tradicionales, resultando en archivos más pequeños con mejor calidad visual.

### Características Principales

- ✅ **Conversión en lote**: Procesa múltiples archivos JPG automáticamente
- 📏 **Redimensionamiento inteligente**: Mantiene la relación de aspecto mientras ajusta al tamaño máximo especificado
- 🎯 **Control de calidad**: Configuración personalizable de calidad de compresión
- ⚡ **Optimización de velocidad**: Control del esfuerzo de compresión para balance velocidad/calidad
- 📊 **Estadísticas detalladas**: Reportes de compresión y espacio ahorrado
- 🔍 **Verificación de dependencias**: Validación automática de FFmpeg
- 🎨 **Interfaz colorida**: Output visual con emojis y colores para mejor experiencia de usuario

### Requisitos del Sistema

#### Dependencias Obligatorias
- **FFmpeg**: Se requiere FFmpeg con soporte para codec libaom-av1
  - Descargar desde: https://ffmpeg.org/download.html
  - Verificar que esté en el PATH del sistema
  - El script verifica automáticamente la disponibilidad

#### Compatibilidad
- **Sistema Operativo**: Windows (PowerShell 5.1+)
- **Memoria**: Recomendado 8GB+ RAM para imágenes de alta resolución
- **Espacio**: Espacio libre suficiente para archivos de salida

### Parámetros

| Parámetro | Tipo | Requerido | Default | Descripción |
|-----------|------|-----------|---------|-------------|
| `InputDir` | string | ✅ Sí | - | Directorio que contiene los archivos JPG a convertir |
| `OutputDir` | string | ✅ Sí | - | Directorio donde se guardarán los archivos AVIF |
| `MaxSize` | int | ❌ No | 2560 | Tamaño máximo en píxeles (lado más largo) |
| `Quality` | int | ❌ No | 80 | Calidad de compresión (1-100, mayor = mejor calidad) |
| `Effort` | int | ❌ No | 3 | Esfuerzo de compresión (0-9, mayor = más lento pero mejor compresión) |

### Uso

#### Uso Básico
```powershell
.\Convert-JPGToAVIF.ps1 -InputDir "C:\imagenes\originales" -OutputDir "C:\imagenes\optimizadas"
```

#### Uso Avanzado con Parámetros Personalizados
```powershell
.\Convert-JPGToAVIF.ps1 -InputDir ".\assets\1x" -OutputDir ".\assets\avif" -MaxSize 1920 -Quality 85 -Effort 5
```

#### Mostrar Ayuda
```powershell
.\Convert-JPGToAVIF.ps1 -help
# o
.\Convert-JPGToAVIF.ps1 --help
# o
.\Convert-JPGToAVIF.ps1 -h
```

### Ejemplos de Configuración por Tipo de Imagen

#### Para Imágenes de Productos (Alta Calidad)
```powershell
.\Convert-JPGToAVIF.ps1 -InputDir ".\productos" -OutputDir ".\productos-avif" -MaxSize 2560 -Quality 90 -Effort 6
```

#### Para Imágenes de Galería (Balance Calidad/Tamaño)
```powershell
.\Convert-JPGToAVIF.ps1 -InputDir ".\galeria" -OutputDir ".\galeria-avif" -MaxSize 1920 -Quality 80 -Effort 4
```

#### Para Thumbnails (Tamaño Pequeño)
```powershell
.\Convert-JPGToAVIF.ps1 -InputDir ".\thumbnails" -OutputDir ".\thumbnails-avif" -MaxSize 800 -Quality 75 -Effort 2
```

### Algoritmo de Conversión

1. **Verificación de Dependencias**: Valida que FFmpeg esté instalado y disponible
2. **Validación de Directorios**: Verifica que el directorio de entrada exista y crea el de salida si es necesario
3. **Escaneo de Archivos**: Busca todos los archivos con extensión `.jpg` en el directorio de entrada
4. **Procesamiento Individual**:
   - Obtiene las dimensiones originales usando `ffprobe`
   - Calcula las nuevas dimensiones manteniendo la relación de aspecto
   - Convierte el parámetro de calidad a CRF (Constant Rate Factor) para AV1
   - Ejecuta FFmpeg con los parámetros optimizados
   - Calcula estadísticas de compresión
5. **Reporte Final**: Muestra estadísticas consolidadas del proceso

### Configuración de Calidad vs CRF

El script convierte automáticamente el parámetro de calidad (1-100) al valor CRF usado por AV1:

| Quality | CRF | Uso Recomendado |
|---------|-----|-----------------|
| 95-100  | 2-7 | Archivos maestros, sin pérdida visible |
| 85-94   | 8-15 | Imágenes de productos premium |
| 75-84   | 16-25 | Uso general, balance calidad/tamaño |
| 65-74   | 26-33 | Previews, imágenes secundarias |
| 50-64   | 34-43 | Thumbnails, imágenes pequeñas |

**Fórmula**: `CRF = 51 - (Quality * 0.51)`

### Configuración de Esfuerzo

| Effort | Velocidad | Calidad | Uso Recomendado |
|--------|-----------|---------|-----------------|
| 0-1    | Muy rápido | Menor | Pruebas, desarrollo |
| 2-3    | Rápido | Buena | Uso general |
| 4-5    | Medio | Mejor | Producción |
| 6-7    | Lento | Excelente | Archivos finales |
| 8-9    | Muy lento | Máxima | Solo para archivos críticos |

### Beneficios del Formato AVIF

- **Compresión Superior**: 30-50% menor tamaño que JPEG con la misma calidad visual
- **Mejor que WebP**: 10-20% menor tamaño que WebP en la mayoría de casos
- **Soporte de HDR**: Capacidad para imágenes de alto rango dinámico
- **Transparencia**: Soporte nativo para canal alpha
- **Estándar Abierto**: Basado en el codec AV1 de Alliance for Open Media

### Compatibilidad de Navegadores AVIF

| Navegador | Versión Mínima | Soporte |
|-----------|---------------|---------|
| Chrome | 85+ | ✅ Completo |
| Firefox | 93+ | ✅ Completo |
| Safari | 16+ | ✅ Completo |
| Edge | 85+ | ✅ Completo |
| Opera | 71+ | ✅ Completo |

### Estructura de Output

```
OutputDir/
├── imagen1.avif
├── imagen2.avif
├── imagen3.avif
└── ...
```

Los archivos AVIF mantienen el mismo nombre base que los archivos JPG originales, solo cambiando la extensión.

### Logging y Monitoreo

El script proporciona información detallada durante el proceso:

- 🔍 Verificación de dependencias
- 📁 Validación de directorios
- 📸 Contador de archivos encontrados
- 📐 Dimensiones originales y nuevas para cada imagen
- ✅/❌ Estado de cada conversión
- 💾 Estadísticas de compresión por archivo
- 📊 Resumen final con totales

### Solución de Problemas

#### Error: "FFmpeg no encontrado"
```
❌ FFmpeg no encontrado. Se requiere FFmpeg para procesar las imágenes.
💡 Instalar desde: https://ffmpeg.org/download.html
```
**Solución**: Instalar FFmpeg y agregarlo al PATH del sistema.

#### Error: "El directorio de entrada no existe"
```
❌ El directorio de entrada no existe: C:\ruta\inexistente
```
**Solución**: Verificar que la ruta del directorio de entrada sea correcta.

#### Error: "No se pudieron obtener dimensiones"
```
WARNING: No se pudieron obtener dimensiones de: archivo.jpg
```
**Solución**: El archivo puede estar corrupto. Verificar integridad del archivo JPG.

#### Error de conversión FFmpeg
**Posibles causas**:
- Archivo de entrada corrupto
- Espacio insuficiente en disco
- Permisos de escritura en directorio de salida
- FFmpeg sin soporte para libaom-av1

### Consideraciones de Performance

#### Factores que Afectan la Velocidad
- **Tamaño de imagen**: Imágenes más grandes requieren más tiempo
- **Parámetro Effort**: Valores altos (6-9) significativamente más lentos
- **Resolución de salida**: El redimensionamiento añade tiempo de procesamiento
- **Hardware**: CPU con más núcleos acelera la codificación AV1

#### Optimización Recomendada
- Para lotes grandes: usar Effort 2-3
- Para máxima calidad: usar Effort 5-6
- Procesar en paralelo: ejecutar múltiples instancias en subdirectorios

### Integración con Workflow de E-commerce

Este script se integra perfectamente con el workflow de optimización de imágenes de Madam Supply:

1. **Preparación**: Colocar imágenes JPG originales en directorio fuente
2. **Conversión**: Ejecutar script con parámetros apropiados para el tipo de imagen
3. **Implementación**: Usar archivos AVIF como formato principal con fallback a WebP/JPEG
4. **Monitoreo**: Revisar estadísticas de compresión para validar beneficios

### Mantenimiento

- **Actualizar FFmpeg**: Mantener FFmpeg actualizado para mejores optimizaciones
- **Ajustar Parámetros**: Revisar periódicamente configuraciones de calidad según feedback
- **Monitorear Compatibilidad**: Verificar soporte de navegadores objetivo

### Historial de Versiones

- **v1.0**: Versión inicial con conversión básica JPG a AVIF
- Soporte para redimensionamiento automático
- Integración completa con FFmpeg
- Sistema de reportes y estadísticas

---

*Script desarrollado para optimización de imágenes del e-commerce Madam Supply. Parte del sistema de assets optimizados para máximo performance web.*

## Madam Carousel Enhancer Plugin Documentation

For details on how the Madam Carousel Enhancer plugin works, refer to the `docs/movimiento_plugin.md` file in the main project directory. 

In summary, the plugin enhances existing Elementor (Swiper.js) and WooCommerce (FlexSlider) carousels by improving visual aspects like fade-in effects, spacing, lazy loading, and transitions. It does not provide its own carousel movement logic.
