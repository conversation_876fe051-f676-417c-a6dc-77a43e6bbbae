<script>
    document.addEventListener('DOMContentLoaded', function() {
      const sourceContainer = document.getElementById('source-container');
      const targetContainer = document.getElementById('target-container');
      const footerContainer = document.querySelector('.madam-footer-home');
      const mergeGradientSection = document.querySelector('.madam-section-merge-gradient');
      
      if (!sourceContainer || !targetContainer) {
        console.error('No se encontraron los contenedores necesarios');
        return;
      }
      
      function getResponsiveValue(paramName) {
        const width = window.innerWidth;
        const suffix = width < 768 ? '-mobile' : width < 1025 ? '-tablet' : '-desktop';
        return sourceContainer.getAttribute(`data-${paramName}${suffix}`) || 
               sourceContainer.getAttribute(`data-${paramName}`);
      }
      
      function setLogoColor(color) {
        const svgElement = document.querySelector('.logo-madam-horizontal-svg svg');
        if (svgElement) {
          svgElement.querySelectorAll('path').forEach(path => path.setAttribute('fill', color));
          return;
        }
        
        const logoImg = document.querySelector('.logo-madam-horizontal-svg img');
        if (!logoImg) return;
        
        fetch(logoImg.src)
          .then(response => response.text())
          .then(svgText => {
            const parser = new DOMParser();
            const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');
            
            svgDoc.querySelectorAll('.letter').forEach(letter => {
              letter.setAttribute('fill', color);
            });
            
            const modifiedSvgText = new XMLSerializer().serializeToString(svgDoc);
            logoImg.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(modifiedSvgText);
          })
          .catch(() => {
            // Fallback simple si todo lo demás falla
            const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1080 160">
              <g fill="${color}"><!-- Paths para las letras MADAM --></g>
            </svg>`;
            logoImg.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgContent);
          });
      }
      
      function applyStyles() {
        // Altura del contenedor
        const gradientHeight = getResponsiveValue('gradient-height');
        if (gradientHeight) {
          targetContainer.style.minHeight = gradientHeight;
          // Aplicar el mismo valor como margen superior negativo
          if (mergeGradientSection) {
            mergeGradientSection.style.height = gradientHeight;
            mergeGradientSection.style.marginTop = `-${gradientHeight}`;
          }
        }
        
        // Aplicar el degradado
        const gradientColor = sourceContainer.getAttribute('data-gradient-color') || '#C4B9AF';
        const gradientDirection = sourceContainer.getAttribute('data-gradient-direction') || 'to bottom';
        
        targetContainer.style.background = `linear-gradient(${gradientDirection}, transparent, ${gradientColor})`;
        if (footerContainer) footerContainer.style.backgroundColor = gradientColor;
        
        // Color del logo
        const logoColor = sourceContainer.getAttribute('data-logo-color');
        if (logoColor) setLogoColor(logoColor);
        
        // Color del menú
        const menuColor = sourceContainer.getAttribute('data-menu-color');
        const menuItems = document.querySelectorAll('.text-menu-footer-home a');
        if (menuItems.length > 0 && menuColor) {
          menuItems.forEach(item => item.style.color = menuColor);
        }
      }
      
      // Aplicar estilos iniciales
      applyStyles();
      
      // Observer para cambios en atributos
      new MutationObserver(() => applyStyles())
        .observe(sourceContainer, { attributes: true });
      
      // Listener para responsive con debounce
      let resizeTimeout;
      window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(applyStyles, 250);
      });
      
      // Asegurar aplicación después de carga completa
      window.addEventListener('load', () => setTimeout(applyStyles, 100));
    });
</script>    
