# 🔍 Herramientas de Diagnóstico - Carrusel Móvil

Este conjunto de herramientas está diseñado para diagnosticar y solucionar el problema del "brinquito" en el carrusel móvil de Madam Online Store.

## 🚀 Inicio Rápido

### 1. Instalación
```bash
cd diagnostics
npm install
npm run setup
```

### 2. Configuración
Antes de ejecutar las pruebas, asegúrate de:
- Tener tu servidor local ejecutándose
- Actualizar la URL en los archivos de configuración si es necesario

### 3. Ejecutar Diagnóstico Completo
```bash
npm run test:all
# O ejecutar el script principal
node run-diagnostics.js
```

## 🛠️ Herramientas Incluidas

### 1. **Analizador CSS** (`css-analyzer.js`)
- ✅ Detecta problemas en media queries
- ✅ Identifica configuraciones contradictorias
- ✅ Analiza transiciones problemáticas
- ✅ Verifica alturas responsive

```bash
node css-analyzer.js
```

### 2. **Diagnóstico Puppeteer** (`carousel-puppeteer-test.js`)
- ✅ Prueba en múltiples dispositivos móviles
- ✅ Simula interacciones táctiles
- ✅ Captura screenshots comparativos
- ✅ Detecta "brinquitos" visuales

```bash
npm run test:puppeteer
```

### 3. **Tests Playwright** (`carousel-mobile-test.js`)
- ✅ Tests automatizados en diferentes navegadores
- ✅ Grabación de video de las pruebas
- ✅ Reportes detallados HTML

```bash
npm run test:playwright
```

### 4. **Script Principal** (`run-diagnostics.js`)
- ✅ Ejecuta todos los diagnósticos
- ✅ Genera reporte consolidado
- ✅ Proporciona recomendaciones específicas

```bash
node run-diagnostics.js
```

## 📊 Interpretación de Resultados

### Niveles de Severidad
- 🔴 **ALTO**: Problemas críticos que afectan la funcionalidad
- 🟡 **MEDIO**: Problemas que afectan la experiencia de usuario
- 🟢 **BAJO**: Optimizaciones menores

### Tipos de Problemas Detectados

#### CSS
- `BREAKPOINT_CONFLICTO`: Conflictos entre media queries
- `ALTURA_CONTRADICTORIA`: Configuraciones de altura inconsistentes
- `TRANSICION_INSTANTANEA`: Transiciones que causan saltos visuales
- `TIMING_FUNCTION_PROBLEMATICO`: Funciones de timing inadecuadas

#### JavaScript/Comportamiento
- `CENTERED_SLIDES_PROBLEMA`: Configuración problemática de centeredSlides
- `SLIDES_PER_VIEW_INCORRECTO`: Configuración inadecuada para móvil
- `INICIALIZACION_TARDIA`: Problemas en la inicialización del carrusel

## 🔧 Soluciones Implementadas

### Cambios Realizados en el Código

1. **Configuración Móvil Optimizada**:
   ```javascript
   // ANTES (problemático)
   centeredSlides: true,
   slidesPerView: 'auto',
   spaceBetween: 0
   
   // DESPUÉS (optimizado)
   centeredSlides: false,  // Evita brincos
   slidesPerView: 1,       // Muestra exactamente 1 slide
   spaceBetween: 10,       // Espacio entre slides
   initialSlide: 0         // Inicia en el primer slide
   ```

2. **Diagnóstico Integrado**:
   - Logs detallados del estado del carrusel
   - Verificación de configuración en tiempo real
   - Detección automática de problemas

## 📁 Estructura de Archivos

```
diagnostics/
├── package.json                    # Dependencias y scripts
├── playwright.config.js           # Configuración Playwright
├── README.md                      # Esta documentación
├── run-diagnostics.js             # Script principal
├── css-analyzer.js                # Analizador CSS
├── carousel-mobile-test.js        # Tests Playwright
├── carousel-puppeteer-test.js     # Diagnóstico Puppeteer
├── screenshots/                   # Capturas de pantalla
├── test-results/                  # Resultados de tests
└── reports/                       # Reportes generados
    ├── diagnostico-completo.json
    └── diagnostico-completo.html
```

## 🎯 Casos de Uso Específicos

### Problema: "Brinquito" al cargar
```bash
# Ejecutar diagnóstico específico
node carousel-puppeteer-test.js

# Revisar logs en consola del navegador
# Buscar: "DIAGNÓSTICO MÓVIL - Carrusel"
```

### Problema: No se ve de 1 imagen
```bash
# Analizar configuración CSS
node css-analyzer.js

# Verificar configuración de slidesPerView
# Revisar media queries móviles
```

### Problema: Swipe no funciona correctamente
```bash
# Test completo con simulación táctil
npm run test:playwright

# Revisar video grabado en test-results/
```

## 🔍 Debugging Avanzado

### Habilitar Logs Detallados
En el navegador, abre la consola y busca mensajes que contengan:
- `🔍 DIAGNÓSTICO MÓVIL`
- `📱 Estado inicial`
- `📱 Después de configuración`

### Verificar Estado del Swiper
```javascript
// En la consola del navegador
const carousel = document.querySelector('.stepless-ratio-carousel');
const swiper = carousel.querySelector('.swiper').swiper;
console.log('Estado actual:', {
    activeIndex: swiper.activeIndex,
    slidesPerView: swiper.params.slidesPerView,
    centeredSlides: swiper.params.centeredSlides
});
```

## 📞 Soporte

Si encuentras problemas o necesitas ayuda adicional:

1. **Revisa los logs**: Siempre verifica la consola del navegador
2. **Ejecuta diagnóstico completo**: `node run-diagnostics.js`
3. **Revisa el reporte HTML**: Más fácil de leer que el JSON
4. **Compara screenshots**: Antes y después de los cambios

## 🚀 Próximos Pasos

Después de ejecutar el diagnóstico:

1. **Implementar correcciones sugeridas**
2. **Probar en dispositivos reales**
3. **Validar rendimiento**
4. **Monitorear en producción**

---

**Nota**: Asegúrate de tener tu servidor local ejecutándose antes de ejecutar las pruebas. Actualiza las URLs en los archivos de configuración según tu entorno.
