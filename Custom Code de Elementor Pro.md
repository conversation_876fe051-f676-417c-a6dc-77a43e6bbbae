# Características de la interfaz de Custom Code de Elementor Pro

## Acceso a la interfaz

1. **Ubicación en el panel principal**: La funcionalidad Custom Code se encuentra en el panel de WordPress, bajo la sección de Elementor.
2. **Navegación sencilla**: Para acceder a ella, debes ir al Dashboard de WordPress, luego hacer clic en "Elementor" en el menú lateral.

## Componentes de la interfaz

1. **Panel de administración centralizado**: Ofrece una interfaz dedicada exclusivamente para gestionar todos tus fragmentos de código personalizados desde un solo lugar.
2. **Opciones de creación**: Incluye un botón para crear nuevos fragmentos de código personalizado fácilmente.
3. **Campos principales**:
    - Campo para nombrar tu código personalizado
    - Editor de código con resaltado de sintaxis para HTML, CSS y JavaScript
    - Opciones para seleccionar la ubicación donde se mostrará el código
4. **Selectores de ubicación**: Te permite elegir dónde quieres insertar el código:
    - En el HEAD del documento
    - Al principio del BODY
    - Al final del BODY
    - Configuración de prioridad para controlar el orden de ejecución
5. **Opciones de filtrado**: Te permite organizar y buscar entre tus códigos personalizados cuando tienes múltiples fragmentos.

## Funcionalidades adicionales

1. **Gestión visual**: Interfaz visual amigable que no requiere editar archivos del tema directamente.
2. **Condiciones de visualización**: Permite establecer condiciones para controlar cuándo y dónde se ejecutará el código personalizado en tu sitio.
3. **Vista previa**: Opción para previsualizar los efectos del código antes de publicarlo definitivamente.
4. **Rendimiento optimizado**: Integración con el DOM optimizado de Elementor para mantener el rendimiento del sitio mientras se añaden funcionalidades personalizadas.

Esta interfaz de Custom Code forma parte de Elementor Pro y está diseñada para ser intuitiva tanto para desarrolladores como para usuarios que quieran implementar códigos de seguimiento, personalizaciones o funcionalidades adicionales sin necesidad de modificar archivos del tema o utilizar plugins adicionales.