{"content": [{"id": "55a00aaf", "settings": {"flex_direction": "row", "flex_justify_content": "center", "flex_align_items": "center", "width": {"unit": "vw", "size": "", "sizes": []}, "width_tablet": {"unit": "vw", "size": "", "sizes": []}, "width_mobile": {"unit": "vw", "size": "", "sizes": []}, "boxed_width": {"unit": "vw", "size": 88, "sizes": []}, "boxed_width_tablet": {"unit": "vw", "size": "", "sizes": []}, "boxed_width_mobile": {"unit": "vw", "size": "", "sizes": []}, "flex_gap": {"column": "0", "row": "0", "isLinked": true, "unit": "px", "size": 0}, "position": "absolute", "z_index": 1000, "__globals__": {"background_color": "", "background_overlay_color": ""}, "flex_direction_mobile": "column", "flex_justify_content_mobile": "space-evenly", "flex_align_items_mobile": "flex-start", "flex_wrap_mobile": "nowrap", "css_classes": "madam-header-home", "padding": {"unit": "vw", "top": "1.24", "right": "1.24", "bottom": "1.24", "left": "1.24", "isLinked": true}, "padding_tablet": {"unit": "vw", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "padding_mobile": {"unit": "vw", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}}, "elements": [{"id": "1330540e", "settings": {"padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "content_width": "full", "flex_direction": "row", "flex_gap": {"column": "0", "row": "0", "isLinked": true, "unit": "px", "size": 0}, "hide_mobile": "hidden-mobile"}, "elements": [{"id": "69cffcc", "settings": {"text": "Tienda", "__dynamic__": [], "__globals__": {"background_color": "globals/colors?id=ee449bb", "button_text_color": "globals/colors?id=text", "typography_typography": "globals/typography?id=78971c7", "hover_color": "globals/colors?id=secondary"}, "text_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "button_text_color": "#000000", "background_color": "#FFFFFF00", "align": "left", "border_border": "none", "_element_width": "initial", "_element_custom_width": {"unit": "%", "size": 50, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}, {"id": "7f481f46", "settings": {"text": "Productos", "__dynamic__": [], "__globals__": {"background_color": "globals/colors?id=ee449bb", "button_text_color": "globals/colors?id=text", "typography_typography": "globals/typography?id=78971c7", "hover_color": "globals/colors?id=secondary"}, "text_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "button_text_color": "#000000", "background_color": "#FFFFFF00", "align": "left", "border_border": "none", "_element_width": "initial", "_element_custom_width": {"unit": "%", "size": 50, "sizes": []}, "hover_color": "#54595F"}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "container"}, {"id": "2a354146", "settings": {"content_width": "full", "width": {"unit": "vw", "size": 24, "sizes": []}, "width_tablet": {"unit": "vw", "size": 28, "sizes": []}, "width_mobile": {"unit": "vw", "size": "", "sizes": []}, "boxed_width": {"unit": "vw", "size": 12, "sizes": []}, "boxed_width_tablet": {"unit": "vw", "size": "", "sizes": []}, "boxed_width_mobile": {"unit": "vw", "size": "", "sizes": []}, "flex_align_items": "center", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "flex_align_items_mobile": "center", "flex_direction_mobile": "row", "flex_justify_content_mobile": "space-between", "_title": "Container"}, "elements": [{"id": "21a5dfe3", "settings": {"__dynamic__": {"image": "[elementor-tag id=\"\" name=\"site-logo\" settings=\"%7B%7D\"]"}, "width": {"unit": "%", "size": 100, "sizes": []}, "width_tablet": {"unit": "vw", "size": "", "sizes": []}, "width_mobile": {"unit": "%", "size": 100, "sizes": []}, "height": {"unit": "%", "size": "", "sizes": []}, "height_tablet": {"unit": "%", "size": "", "sizes": []}, "height_mobile": {"unit": "vh", "size": "", "sizes": []}, "image_border_border": "none", "_element_width": "initial", "_element_custom_width": {"unit": "vw", "size": 10, "sizes": []}, "_element_custom_width_tablet": {"unit": "%", "size": 100, "sizes": []}, "_element_custom_width_mobile": {"unit": "vw", "size": 27, "sizes": []}, "_offset_x": {"unit": "px", "size": "", "sizes": []}, "_offset_y": {"unit": "px", "size": "", "sizes": []}, "custom_css": "selector.elementor-widget-image {\n    margin-top: 0;\n    line-height: 0;\n}", "object-fit_mobile": "contain", "_element_width_tablet": "inherit", "_element_width_mobile": "initial"}, "elements": [], "isInner": false, "widgetType": "theme-site-logo", "elType": "widget"}, {"id": "7b628407", "settings": {"selected_icon": {"value": "fas fa-bars", "library": "fa-solid"}, "size_mobile": {"unit": "vw", "size": 5, "sizes": []}, "hide_desktop": "hidden-desktop", "hide_tablet": "hidden-tablet"}, "elements": [], "isInner": false, "widgetType": "icon", "elType": "widget"}], "isInner": true, "elType": "container"}, {"id": "7c76b149", "settings": {"padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "content_width": "full", "boxed_width": {"unit": "%", "size": "", "sizes": []}, "boxed_width_tablet": {"unit": "%", "size": "", "sizes": []}, "boxed_width_mobile": {"unit": "%", "size": "", "sizes": []}, "flex_direction": "row", "flex_gap": {"column": "0", "row": "0", "isLinked": true, "unit": "px", "size": 0}, "hide_mobile": "hidden-mobile"}, "elements": [{"id": "11d41303", "settings": {"text": "Contacto", "__dynamic__": [], "__globals__": {"background_color": "globals/colors?id=ee449bb", "button_text_color": "globals/colors?id=text", "typography_typography": "globals/typography?id=78971c7", "hover_color": "globals/colors?id=secondary"}, "text_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "button_text_color": "#000000", "background_color": "#FFFFFF00", "align": "right", "border_border": "none", "_element_width": "initial", "_element_custom_width": {"unit": "%", "size": 50, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}, {"id": "879808e", "settings": {"text": "<PERSON><PERSON>", "__dynamic__": {"link": "[elementor-tag id=\"e6baa89\" name=\"internal-url\" settings=\"%7B%22type%22%3A%22post%22%2C%22post_id%22%3A%227%22%7D\"]"}, "__globals__": {"background_color": "globals/colors?id=ee449bb", "button_text_color": "globals/colors?id=text", "typography_typography": "globals/typography?id=78971c7", "hover_color": "globals/colors?id=secondary"}, "text_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "button_text_color": "#000000", "background_color": "#FFFFFF00", "align": "right", "border_border": "none", "_element_width": "initial", "_element_custom_width": {"unit": "%", "size": 50, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "container"}], "isInner": false, "elType": "container"}], "page_settings": {"content_wrapper_html_tag": "header", "__globals__": {"background_color": ""}}, "version": "0.4", "title": "<PERSON><PERSON><PERSON>", "type": "header"}