// ANALIZADOR CSS - Detectar problemas de responsive en el carrusel
const fs = require('fs');
const path = require('path');

class CarouselCSSAnalyzer {
    constructor() {
        this.problemas = [];
        this.sugerencias = [];
    }
    
    analizarCSS(cssContent) {
        console.log('🔍 Analizando CSS del carrusel...');
        
        // Análisis 1: Verificar media queries
        this.analizarMediaQueries(cssContent);
        
        // Análisis 2: Verificar propiedades problemáticas
        this.analizarPropiedadesProblematicas(cssContent);
        
        // Análisis 3: Verificar transiciones y transforms
        this.analizarTransiciones(cssContent);
        
        // Análisis 4: Verificar configuración de altura
        this.analizarAlturas(cssContent);
        
        return this.generarReporte();
    }
    
    analizarMediaQueries(css) {
        const mediaQueries = css.match(/@media[^{]+\{[^{}]*\{[^}]*\}[^}]*\}/g) || [];
        
        console.log(`📱 Media queries encontradas: ${mediaQueries.length}`);
        
        // Verificar breakpoints
        const breakpoints = [];
        mediaQueries.forEach(mq => {
            const match = mq.match(/\((?:max-width|min-width):\s*(\d+)px\)/g);
            if (match) {
                match.forEach(bp => {
                    const width = bp.match(/(\d+)px/)[1];
                    breakpoints.push(parseInt(width));
                });
            }
        });
        
        // Detectar problemas en breakpoints
        if (breakpoints.includes(768) && breakpoints.includes(480)) {
            this.problemas.push({
                tipo: 'BREAKPOINT_CONFLICTO',
                descripcion: 'Hay breakpoints en 768px y 480px que pueden causar conflictos',
                linea: 'Media queries móviles',
                solucion: 'Considerar usar solo 768px como breakpoint principal móvil'
            });
        }
        
        // Verificar si hay configuraciones contradictorias
        const mobileConfigs = mediaQueries.filter(mq => 
            mq.includes('max-width: 768px') || mq.includes('max-width: 480px')
        );
        
        mobileConfigs.forEach(config => {
            if (config.includes('max-height: 40vh') && config.includes('max-height: 52vh')) {
                this.problemas.push({
                    tipo: 'ALTURA_CONTRADICTORIA',
                    descripcion: 'Configuraciones de altura contradictorias en móvil',
                    solucion: 'Unificar la altura máxima para móvil'
                });
            }
        });
    }
    
    analizarPropiedadesProblematicas(css) {
        // Verificar centeredSlides en CSS (aunque se configure en JS)
        if (css.includes('centeredSlides') || css.includes('centered-slides')) {
            this.problemas.push({
                tipo: 'CENTERED_SLIDES_CSS',
                descripcion: 'Configuración de centeredSlides detectada en CSS',
                solucion: 'Mover configuración a JavaScript para mejor control'
            });
        }
        
        // Verificar transforms problemáticos
        const transforms = css.match(/transform:\s*[^;]+;/g) || [];
        transforms.forEach(transform => {
            if (transform.includes('translateX') && !transform.includes('translate3d')) {
                this.sugerencias.push({
                    tipo: 'OPTIMIZACION_TRANSFORM',
                    descripcion: 'Usar translate3d en lugar de translateX para mejor rendimiento',
                    actual: transform,
                    sugerido: transform.replace('translateX', 'translate3d').replace(')', ', 0, 0)')
                });
            }
        });
        
        // Verificar will-change
        if (!css.includes('will-change: transform')) {
            this.sugerencias.push({
                tipo: 'WILL_CHANGE_FALTANTE',
                descripcion: 'Agregar will-change: transform para optimizar animaciones',
                solucion: 'Agregar will-change: transform al .swiper-wrapper'
            });
        }
    }
    
    analizarTransiciones(css) {
        const transiciones = css.match(/transition[^;]*;/g) || [];
        
        transiciones.forEach(transicion => {
            // Verificar transiciones muy rápidas que pueden causar "brinquitos"
            if (transicion.includes('0s') || transicion.includes('0ms')) {
                this.problemas.push({
                    tipo: 'TRANSICION_INSTANTANEA',
                    descripcion: 'Transición instantánea puede causar saltos visuales',
                    actual: transicion,
                    solucion: 'Usar una duración mínima como 0.1s'
                });
            }
            
            // Verificar timing functions problemáticos
            if (transicion.includes('linear') && !transicion.includes('desktop-stepless')) {
                this.problemas.push({
                    tipo: 'TIMING_FUNCTION_PROBLEMATICO',
                    descripcion: 'Timing function linear en móvil puede causar movimiento brusco',
                    solucion: 'Usar ease-out o ease-in-out para móvil'
                });
            }
        });
    }
    
    analizarAlturas(css) {
        const alturas = css.match(/(?:max-)?height:\s*[^;]+;/g) || [];
        
        // Verificar alturas en vh que pueden ser problemáticas en móvil
        alturas.forEach(altura => {
            if (altura.includes('vh')) {
                const valor = altura.match(/(\d+)vh/);
                if (valor && parseInt(valor[1]) > 50) {
                    this.problemas.push({
                        tipo: 'ALTURA_VH_EXCESIVA',
                        descripcion: `Altura de ${valor[1]}vh puede ser excesiva en móvil`,
                        actual: altura,
                        solucion: 'Considerar usar valores más conservadores como 40vh para móvil'
                    });
                }
            }
        });
        
        // Verificar configuraciones de altura inconsistentes
        const alturasMovil = [];
        const mediaQueryMovil = css.match(/@media[^{]*max-width:\s*768px[^}]*\}/g);
        if (mediaQueryMovil) {
            mediaQueryMovil.forEach(mq => {
                const alturasEnMQ = mq.match(/(?:max-)?height:\s*[^;]+;/g) || [];
                alturasMovil.push(...alturasEnMQ);
            });
        }
        
        if (alturasMovil.length > 2) {
            this.problemas.push({
                tipo: 'ALTURAS_INCONSISTENTES',
                descripcion: 'Múltiples configuraciones de altura en móvil pueden causar inconsistencias',
                solucion: 'Unificar las configuraciones de altura para móvil'
            });
        }
    }
    
    generarReporte() {
        const reporte = {
            timestamp: new Date().toISOString(),
            resumen: {
                problemasEncontrados: this.problemas.length,
                sugerenciasOptimizacion: this.sugerencias.length,
                nivelSeveridad: this.calcularSeveridad()
            },
            problemas: this.problemas,
            sugerencias: this.sugerencias,
            recomendacionesGenerales: this.generarRecomendaciones()
        };
        
        return reporte;
    }
    
    calcularSeveridad() {
        const problemasAltos = this.problemas.filter(p => 
            ['BREAKPOINT_CONFLICTO', 'TRANSICION_INSTANTANEA', 'ALTURA_VH_EXCESIVA'].includes(p.tipo)
        ).length;
        
        if (problemasAltos > 2) return 'ALTO';
        if (this.problemas.length > 3) return 'MEDIO';
        return 'BAJO';
    }
    
    generarRecomendaciones() {
        const recomendaciones = [];
        
        if (this.problemas.some(p => p.tipo.includes('ALTURA'))) {
            recomendaciones.push({
                categoria: 'RESPONSIVE',
                titulo: 'Optimizar alturas para móvil',
                descripcion: 'Usar alturas más conservadoras y consistentes en dispositivos móviles',
                prioridad: 'ALTA'
            });
        }
        
        if (this.problemas.some(p => p.tipo.includes('TRANSICION'))) {
            recomendaciones.push({
                categoria: 'ANIMACIONES',
                titulo: 'Suavizar transiciones',
                descripcion: 'Evitar transiciones instantáneas y usar timing functions apropiados',
                prioridad: 'ALTA'
            });
        }
        
        if (this.sugerencias.some(s => s.tipo.includes('TRANSFORM'))) {
            recomendaciones.push({
                categoria: 'RENDIMIENTO',
                titulo: 'Optimizar transforms',
                descripcion: 'Usar translate3d y will-change para mejor rendimiento',
                prioridad: 'MEDIA'
            });
        }
        
        return recomendaciones;
    }
}

// Función para analizar el archivo CSS actual
async function analizarCarouselCSS() {
    try {
        const cssPath = path.join(__dirname, '..', 'custom-code', 'madam-carousel-stepless-custom-code.html');
        const contenido = fs.readFileSync(cssPath, 'utf8');
        
        // Extraer solo la parte CSS
        const cssMatch = contenido.match(/<style>([\s\S]*?)<\/style>/);
        if (!cssMatch) {
            throw new Error('No se encontró CSS en el archivo');
        }
        
        const css = cssMatch[1];
        
        const analyzer = new CarouselCSSAnalyzer();
        const reporte = analyzer.analizarCSS(css);
        
        // Guardar reporte
        fs.writeFileSync(
            path.join(__dirname, 'reporte-css-analysis.json'),
            JSON.stringify(reporte, null, 2)
        );
        
        console.log('\n📋 REPORTE DE ANÁLISIS CSS:');
        console.log(`⚠️ Problemas encontrados: ${reporte.resumen.problemasEncontrados}`);
        console.log(`💡 Sugerencias: ${reporte.resumen.sugerenciasOptimizacion}`);
        console.log(`🚨 Nivel de severidad: ${reporte.resumen.nivelSeveridad}`);
        
        if (reporte.problemas.length > 0) {
            console.log('\n🔴 PROBLEMAS DETECTADOS:');
            reporte.problemas.forEach((problema, index) => {
                console.log(`${index + 1}. ${problema.tipo}: ${problema.descripcion}`);
                if (problema.solucion) {
                    console.log(`   💡 Solución: ${problema.solucion}`);
                }
            });
        }
        
        if (reporte.sugerencias.length > 0) {
            console.log('\n💡 SUGERENCIAS DE OPTIMIZACIÓN:');
            reporte.sugerencias.forEach((sugerencia, index) => {
                console.log(`${index + 1}. ${sugerencia.tipo}: ${sugerencia.descripcion}`);
            });
        }
        
        return reporte;
        
    } catch (error) {
        console.error('❌ Error al analizar CSS:', error.message);
        return null;
    }
}

// Ejecutar análisis si se llama directamente
if (require.main === module) {
    analizarCarouselCSS();
}

module.exports = { CarouselCSSAnalyzer, analizarCarouselCSS };
