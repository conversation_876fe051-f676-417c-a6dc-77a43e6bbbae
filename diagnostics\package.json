{"name": "madam-carousel-diagnostics", "version": "1.0.0", "description": "Herramientas de diagnóstico para el carrusel móvil de Madam Online Store", "main": "carousel-puppeteer-test.js", "scripts": {"test:playwright": "npx playwright test carousel-mobile-test.js", "test:puppeteer": "node carousel-puppeteer-test.js", "test:css": "node css-analyzer.js", "test:all": "node run-diagnostics.js", "test:quick": "npm run test:css && npm run test:puppeteer", "install:playwright": "npx playwright install", "setup": "npm install && npm run install:playwright", "clean": "rm -rf test-results screenshots reports *.json", "report": "open reports/diagnostico-completo.html || start reports/diagnostico-completo.html"}, "dependencies": {"puppeteer": "^21.5.0"}, "devDependencies": {"@playwright/test": "^1.40.0"}, "keywords": ["carousel", "mobile", "diagnostics", "swiper", "elementor"], "author": "<PERSON>", "license": "MIT"}