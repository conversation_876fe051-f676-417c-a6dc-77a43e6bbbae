# Documentación: Swiper.js en Elementor Pro y FlexSlider en WooCommerce

## Índice
1. [Swiper.js en Elementor Pro](#swiper-js-en-elementor-pro)
2. [FlexSlider en WooCommerce](#flexslider-en-woocommerce)
3. [Comparación y mejores prácticas](#comparación-y-mejores-prácticas)

---

## Swiper.js en Elementor Pro

### Introducción

Elementor Pro utiliza **Swiper.js** como biblioteca principal para sliders y carruseles desde la versión 2.7, reemplazando gradualmente la anterior biblioteca Slick Slider. Swiper.js es más performante, flexible y adecuado para las necesidades modernas de desarrollo web.

### ¿Por qué Swiper.js?

Elementor migró a Swiper.js por las siguientes razones:
- **Mayor rendimiento**: Menos carga de JavaScript y mejor optimización
- **Más flexible**: Amplia gama de opciones y métodos de personalización
- **Touch-friendly**: Mejor soporte para dispositivos móviles y táctiles
- **Mantenimiento activo**: Biblioteca con desarrollo continuo

### Estructura HTML Base

```html
<div class="swiper-container">
  <div class="swiper-wrapper">
    <div class="swiper-slide">Contenido del slide 1</div>
    <div class="swiper-slide">Contenido del slide 2</div>
    <div class="swiper-slide">Contenido del slide 3</div>
  </div>
  
  <!-- Elementos de navegación opcionales -->
  <div class="swiper-pagination"></div>
  <div class="swiper-button-prev"></div>
  <div class="swiper-button-next"></div>
  <div class="swiper-scrollbar"></div>
</div>
```

### Acceso a la Instancia de Swiper en Elementor

Elementor almacena la instancia de Swiper en un atributo `data` del elemento. Para acceder:

```javascript
// Obtener la instancia de Swiper
const swiperElement = document.querySelector('#carousel-widget-id .swiper-container');
const swiperInstance = swiperElement.swiper;

// O usando jQuery (más común en WordPress)
const swiperInstance = jQuery('#carousel-widget-id .swiper-container')[0].swiper;
```

### Implementación Básica Personalizada

```javascript
jQuery(document).ready(function($) {
    // Esperar a que Elementor esté completamente cargado
    $(window).on('elementor/frontend/init', function() {
        
        // Configuración básica
        const mySwiper = new Swiper('.mi-carousel-personalizado', {
            // Configuración de slides
            slidesPerView: 3,
            spaceBetween: 30,
            loop: true,
            centeredSlides: false,
            
            // Autoplay
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            
            // Navegación
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            
            // Paginación
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                type: 'bullets', // 'bullets', 'fraction', 'progressbar'
            },
            
            // Scroll bar
            scrollbar: {
                el: '.swiper-scrollbar',
                draggable: true,
            },
            
            // Responsive breakpoints
            breakpoints: {
                320: {
                    slidesPerView: 1,
                    spaceBetween: 10
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 20
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30
                }
            },
            
            // Callbacks
            on: {
                init: function() {
                    console.log('Swiper inicializado');
                },
                slideChange: function() {
                    console.log('Slide cambiado:', this.activeIndex);
                }
            }
        });
    });
});
```

### Conversión de Elementos Elementor a Swiper

Para convertir cualquier sección de Elementor en un slider:

```javascript
jQuery(window).on('load', function($) {
    // Añadir clases de Swiper a elementos existentes
    $('.elementor-section.mi-slider').addClass('swiper-container');
    $('.elementor-section.mi-slider > .elementor-container').addClass('swiper-wrapper');
    $('.elementor-section.mi-slider .elementor-column').addClass('swiper-slide');
    
    // Añadir elementos de navegación
    $('.mi-slider').append(`
        <div class="swiper-pagination"></div>
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
    `);
    
    // Inicializar Swiper
    const elementorSwiper = new Swiper('.mi-slider', {
        slidesPerView: 'auto',
        spaceBetween: 20,
        loop: true,
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        }
    });
});
```

### Extender Widgets de Productos de Elementor Pro

```javascript
jQuery(document).ready(function($) {
    // Convertir widget de productos en carousel
    function initProductCarousel() {
        const $productContainer = $('.elementor-widget-wc-products');
        
        if ($productContainer.length) {
            // Preparar estructura
            $productContainer.find('.products').addClass('swiper-container');
            $productContainer.find('.products ul').addClass('swiper-wrapper');
            $productContainer.find('.products li').addClass('swiper-slide');
            
            // Añadir controles
            $productContainer.find('.products').append(`
                <div class="swiper-pagination"></div>
                <div class="swiper-button-prev"><i class="fa fa-chevron-left"></i></div>
                <div class="swiper-button-next"><i class="fa fa-chevron-right"></i></div>
            `);
            
            // Inicializar carousel
            new Swiper($productContainer.find('.products')[0], {
                slidesPerView: 4,
                spaceBetween: 30,
                loop: true,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                breakpoints: {
                    320: { slidesPerView: 1, spaceBetween: 10 },
                    768: { slidesPerView: 2, spaceBetween: 20 },
                    1024: { slidesPerView: 3, spaceBetween: 25 },
                    1200: { slidesPerView: 4, spaceBetween: 30 }
                }
            });
        }
    }
    
    // Ejecutar cuando Elementor esté listo
    $(window).on('elementor/frontend/init', initProductCarousel);
});
```

### Métodos y APIs Útiles

```javascript
// Control programático del slider
const swiper = document.querySelector('.swiper-container').swiper;

// Navegación
swiper.slideNext();          // Siguiente slide
swiper.slidePrev();          // Slide anterior
swiper.slideTo(2);           // Ir al slide específico (índice 2)

// Control de autoplay
swiper.autoplay.start();     // Iniciar autoplay
swiper.autoplay.stop();      // Detener autoplay

// Eventos personalizados
swiper.on('slideChange', function() {
    console.log('Slide actual:', this.activeIndex);
});

// Actualizar después de cambios dinámicos
swiper.update();             // Actualizar tamaños y posiciones
swiper.updateSlides();       // Actualizar solo slides
```

---

## FlexSlider en WooCommerce

### Introducción

WooCommerce utiliza **FlexSlider** como biblioteca predeterminada para las galerías de imágenes de productos. FlexSlider es una biblioteca jQuery robusta y confiable que ha sido la base de las galerías de productos durante años.

### Estructura HTML de FlexSlider en WooCommerce

```html
<div class="woocommerce-product-gallery">
    <div class="flex-viewport">
        <ul class="slides">
            <li data-thumb="thumbnail-url.jpg">
                <img src="image-url.jpg" alt="Producto">
            </li>
            <!-- Más slides -->
        </ul>
    </div>
    <ol class="flex-control-nav flex-control-thumbs">
        <li><img src="thumbnail1.jpg" alt=""></li>
        <li><img src="thumbnail2.jpg" alt=""></li>
    </ol>
</div>
```

### Configuración Predeterminada de WooCommerce

```javascript
// Configuración por defecto que usa WooCommerce
jQuery('.woocommerce-product-gallery').flexslider({
    selector: '.woocommerce-product-gallery__wrapper > .woocommerce-product-gallery__image',
    animation: 'slide',
    animationSpeed: 500,
    animationLoop: false,
    slideshow: false,
    controlNav: 'thumbnails',
    directionNav: true,
    prevText: '',
    nextText: '',
    rtl: false
});
```

### Personalización mediante Filtros PHP

#### Filtro Principal: `woocommerce_single_product_carousel_options`

```php
/**
 * Personalizar opciones de FlexSlider en WooCommerce
 */
add_filter('woocommerce_single_product_carousel_options', 'custom_product_carousel_options');

function custom_product_carousel_options($options) {
    return array_merge($options, array(
        'animation'         => 'slide',        // 'slide' o 'fade'
        'animationSpeed'    => 400,            // Velocidad en milisegundos
        'slideshow'         => true,           // Autoplay habilitado
        'slideshowSpeed'    => 4000,           // Tiempo entre slides
        'directionNav'      => true,           // Flechas de navegación
        'controlNav'        => true,           // Navegación por puntos/thumbnails
        'smoothHeight'      => true,           // Altura adaptable
        'animationLoop'     => true,           // Loop infinito
        'touch'             => true,           // Soporte touch
        'keyboard'          => true,           // Control por teclado
        'mousewheel'        => false,          // Control por scroll de mouse
        'pauseOnHover'      => true,           // Pausar en hover
        'rtl'               => is_rtl(),       // Soporte RTL
    ));
}
```

#### Deshabilitar FlexSlider Completamente

```php
/**
 * Deshabilitar FlexSlider en productos
 */
add_action('wp', 'disable_woocommerce_flexslider');

function disable_woocommerce_flexslider() {
    if (is_product()) {
        // Remover soporte de galería del tema
        remove_theme_support('wc-product-gallery-zoom');
        remove_theme_support('wc-product-gallery-lightbox'); 
        remove_theme_support('wc-product-gallery-slider');
    }
}
```

### Personalización Avanzada con JavaScript

#### Reinicialización Completa

```javascript
jQuery(document).ready(function($) {
    // Destruir FlexSlider existente
    $('.woocommerce-product-gallery').flexslider('destroy');
    
    // Reinicializar con configuración personalizada
    $('.woocommerce-product-gallery').flexslider({
        animation: 'slide',
        animationSpeed: 600,
        slideshow: true,
        slideshowSpeed: 5000,
        controlNav: 'thumbnails',
        directionNav: true,
        smoothHeight: true,
        
        // Callbacks personalizados
        start: function(slider) {
            console.log('FlexSlider iniciado');
        },
        
        before: function(slider) {
            console.log('Antes de cambiar slide');
        },
        
        after: function(slider) {
            console.log('Después de cambiar slide');
            // Código personalizado al cambiar slide
        }
    });
});
```

#### Controles Personalizados

```javascript
jQuery(document).ready(function($) {
    const $gallery = $('.woocommerce-product-gallery');
    
    // Controles programáticos
    function nextSlide() {
        $gallery.flexslider('next');
    }
    
    function prevSlide() {
        $gallery.flexslider('prev');
    }
    
    function goToSlide(slideNumber) {
        $gallery.flexslider(slideNumber);
    }
    
    // Añadir controles personalizados
    $gallery.after(`
        <div class="custom-gallery-controls">
            <button class="custom-prev" onclick="prevSlide()">← Anterior</button>
            <button class="custom-next" onclick="nextSlide()">Siguiente →</button>
        </div>
    `);
    
    // Control por teclado personalizado
    $(document).keydown(function(e) {
        if (e.keyCode === 37) { // Flecha izquierda
            prevSlide();
        } else if (e.keyCode === 39) { // Flecha derecha
            nextSlide();
        }
    });
});
```

### Modificar Tamaños de Miniaturas

```php
/**
 * Personalizar tamaños de thumbnails en galería
 */
add_filter('woocommerce_gallery_thumbnail_size', 'custom_gallery_thumbnail_size');

function custom_gallery_thumbnail_size($size) {
    return array(
        'width'  => 100,
        'height' => 100,
        'crop'   => 1
    );
}

/**
 * Modificar imagen principal de galería
 */
add_filter('woocommerce_gallery_image_size', 'custom_gallery_image_size');

function custom_gallery_image_size($size) {
    return 'large'; // o 'medium', 'full', etc.
}
```

### Añadir Funcionalidad de Zoom Personalizada

```javascript
jQuery(document).ready(function($) {
    // Zoom personalizado en hover
    $('.woocommerce-product-gallery__image img').hover(
        function() {
            $(this).css('transform', 'scale(1.2)');
        },
        function() {
            $(this).css('transform', 'scale(1)');
        }
    );
    
    // Lightbox personalizado
    $('.woocommerce-product-gallery__image a').click(function(e) {
        e.preventDefault();
        const imageUrl = $(this).attr('href');
        
        // Abrir en lightbox personalizado
        openCustomLightbox(imageUrl);
    });
});
```

### Hook para Galería Personalizada Completa

```php
/**
 * Reemplazar completamente la galería de productos
 */
remove_action('woocommerce_before_single_product_summary', 'woocommerce_show_product_images', 20);
add_action('woocommerce_before_single_product_summary', 'custom_product_gallery', 20);

function custom_product_gallery() {
    global $product;
    
    $attachment_ids = $product->get_gallery_image_ids();
    $main_image_id = $product->get_image_id();
    
    if ($main_image_id) {
        array_unshift($attachment_ids, $main_image_id);
    }
    
    if (empty($attachment_ids)) {
        return;
    }
    
    echo '<div class="custom-product-gallery">';
    echo '<div class="main-image-container">';
    
    foreach ($attachment_ids as $attachment_id) {
        $image_url = wp_get_attachment_image_url($attachment_id, 'large');
        $thumbnail_url = wp_get_attachment_image_url($attachment_id, 'thumbnail');
        
        echo '<div class="gallery-slide">';
        echo '<img src="' . esc_url($image_url) . '" alt="Producto">';
        echo '</div>';
    }
    
    echo '</div>';
    echo '<div class="thumbnail-navigation">';
    
    foreach ($attachment_ids as $index => $attachment_id) {
        $thumbnail_url = wp_get_attachment_image_url($attachment_id, 'thumbnail');
        echo '<img src="' . esc_url($thumbnail_url) . '" data-slide="' . $index . '" alt="Thumbnail">';
    }
    
    echo '</div>';
    echo '</div>';
}
```

---

## Comparación y Mejores Prácticas

### Comparación Técnica

| Aspecto | Swiper.js (Elementor) | FlexSlider (WooCommerce) |
|---------|----------------------|--------------------------|
| **Tamaño** | ~45KB minificado | ~15KB minificado |
| **Dependencias** | JavaScript puro | Requiere jQuery |
| **Touch Support** | Nativo y avanzado | Básico |
| **Responsive** | Breakpoints flexibles | Configuración manual |
| **API** | Extensa y moderna | Limitada pero estable |
| **Rendimiento** | Superior | Bueno |
| **Compatibilidad** | ES6+ | IE8+ |

### Mejores Prácticas

#### Para Swiper.js en Elementor:

1. **Inicialización Correcta**:
   ```javascript
   // Siempre esperar a que Elementor esté listo
   $(window).on('elementor/frontend/init', function() {
       initSwiper();
   });
   ```

2. **Responsive Design**:
   ```javascript
   breakpoints: {
       320: { slidesPerView: 1 },
       768: { slidesPerView: 2 },
       1024: { slidesPerView: 3 }
   }
   ```

3. **Optimización de Rendimiento**:
   ```javascript
   // Lazy loading para imágenes
   lazy: {
       loadPrevNext: true,
       loadPrevNextAmount: 2
   }
   ```

#### Para FlexSlider en WooCommerce:

1. **Usar Filtros PHP**:
   ```php
   // Siempre usar filtros en lugar de JavaScript directo
   add_filter('woocommerce_single_product_carousel_options', 'custom_options');
   ```

2. **Verificar Existencia**:
   ```javascript
   if (typeof $.fn.flexslider !== 'undefined') {
       $('.gallery').flexslider(options);
   }
   ```

3. **Compatibilidad con Temas**:
   ```php
   // Verificar soporte del tema
   if (current_theme_supports('wc-product-gallery-slider')) {
       // Tu código aquí
   }
   ```

### Consideraciones de Migración

Si necesitas migrar de FlexSlider a Swiper en WooCommerce:

```javascript
// Deshabilitar FlexSlider
add_action('wp_enqueue_scripts', function() {
    if (is_product()) {
        wp_dequeue_script('flexslider');
        wp_enqueue_script('swiper-js');
    }
}, 100);

// Implementar Swiper
jQuery(document).ready(function($) {
    new Swiper('.woocommerce-product-gallery', {
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        thumbs: {
            swiper: thumbnailSwiper
        }
    });
});
```

Esta documentación proporciona una base sólida para trabajar con ambas bibliotecas de sliders en sus respectivos contextos de WordPress/WooCommerce y Elementor Pro.