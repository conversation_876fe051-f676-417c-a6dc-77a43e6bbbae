{"type": "elementor", "siteurl": "https://madam.supply/wp-json/", "elements": [{"id": "3cfba6d", "elType": "container", "isInner": false, "isLocked": false, "settings": {"flex_direction": "row", "min_height": {"unit": "custom", "size": "63vw", "sizes": []}, "min_height_tablet": {"unit": "custom", "size": "63vw", "sizes": []}, "min_height_mobile": {"unit": "custom", "size": "63vw", "sizes": []}, "background_background": "classic", "background_image": {"url": "https://madam.supply/wp-content/uploads/2025/04/modelos-camisetas-elmal-blanco-negro-optimized.webp", "id": 156, "size": "", "alt": "", "source": "library"}, "background_position": "center center", "background_size": "cover", "_attributes": "id|source-container\ndata-gradient-height-desktop|7.5vw\ndata-gradient-height-tablet|10vw\ndata-gradient-height-mobile|33vw\ndata-gradient-color|#C4B9AF\ndata-logo-color|#FFFFFF\ndata-menu-color|#FFFFFF", "container_type": "flex", "content_width": "boxed", "width": {"unit": "%", "size": "", "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": "", "sizes": []}, "boxed_width": {"unit": "px", "size": "", "sizes": []}, "boxed_width_tablet": {"unit": "px", "size": "", "sizes": []}, "boxed_width_mobile": {"unit": "px", "size": "", "sizes": []}, "flex_direction_tablet": "", "flex_direction_mobile": "", "flex__is_row": "row", "flex__is_column": "column", "flex_justify_content": "", "flex_justify_content_tablet": "", "flex_justify_content_mobile": "", "flex_align_items": "", "flex_align_items_tablet": "", "flex_align_items_mobile": "", "flex_gap": {"column": "", "row": "", "isLinked": true, "unit": "px"}, "flex_gap_tablet": {"column": "", "row": "", "isLinked": true, "unit": "px"}, "flex_gap_mobile": {"column": "", "row": "", "isLinked": true, "unit": "px"}, "flex_wrap": "", "flex_wrap_tablet": "", "flex_wrap_mobile": "", "flex_align_content": "", "flex_align_content_tablet": "", "flex_align_content_mobile": "", "grid_outline": "yes", "grid_columns_grid": {"unit": "fr", "size": 3, "sizes": []}, "grid_columns_grid_tablet": {"unit": "fr", "size": "", "sizes": []}, "grid_columns_grid_mobile": {"unit": "fr", "size": 1, "sizes": []}, "grid_rows_grid": {"unit": "fr", "size": 2, "sizes": []}, "grid_rows_grid_tablet": {"unit": "fr", "size": "", "sizes": []}, "grid_rows_grid_mobile": {"unit": "fr", "size": "", "sizes": []}, "grid_gaps": {"column": "", "row": "", "isLinked": true, "unit": "px"}, "grid_gaps_tablet": {"column": "", "row": "", "isLinked": true, "unit": "px"}, "grid_gaps_mobile": {"column": "", "row": "", "isLinked": true, "unit": "px"}, "grid_auto_flow": "row", "grid_auto_flow_tablet": "row", "grid_auto_flow_mobile": "row", "grid_justify_items": "", "grid_justify_items_tablet": "", "grid_justify_items_mobile": "", "grid_align_items": "", "grid_align_items_tablet": "", "grid_align_items_mobile": "", "grid_justify_content": "", "grid_justify_content_tablet": "", "grid_justify_content_mobile": "", "grid_align_content": "", "grid_align_content_tablet": "", "grid_align_content_mobile": "", "grid__is_row": "row", "grid__is_column": "column", "overflow": "", "html_tag": "", "link": {"url": "", "is_external": "", "nofollow": "", "custom_attributes": ""}, "background_color": "", "background_color_stop": {"unit": "%", "size": 0, "sizes": []}, "background_color_stop_tablet": {"unit": "%"}, "background_color_stop_mobile": {"unit": "%"}, "background_color_b": "#f2295b", "background_color_b_stop": {"unit": "%", "size": 100, "sizes": []}, "background_color_b_stop_tablet": {"unit": "%"}, "background_color_b_stop_mobile": {"unit": "%"}, "background_gradient_type": "linear", "background_gradient_angle": {"unit": "deg", "size": 180, "sizes": []}, "background_gradient_angle_tablet": {"unit": "deg"}, "background_gradient_angle_mobile": {"unit": "deg"}, "background_gradient_position": "center center", "background_gradient_position_tablet": "", "background_gradient_position_mobile": "", "background_image_tablet": {"url": "", "id": "", "size": ""}, "background_image_mobile": {"url": "", "id": "", "size": ""}, "background_position_tablet": "center center", "background_position_mobile": "center center", "background_xpos": {"unit": "px", "size": 0, "sizes": []}, "background_xpos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_xpos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_ypos": {"unit": "px", "size": 0, "sizes": []}, "background_ypos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_ypos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_attachment": "", "background_repeat": "", "background_repeat_tablet": "", "background_repeat_mobile": "", "background_size_tablet": "", "background_size_mobile": "", "background_bg_width": {"unit": "%", "size": 100, "sizes": []}, "background_bg_width_tablet": {"unit": "px", "size": "", "sizes": []}, "background_bg_width_mobile": {"unit": "px", "size": "", "sizes": []}, "background_video_link": "", "background_video_start": "", "background_video_end": "", "background_play_once": "", "background_play_on_mobile": "", "background_privacy_mode": "", "background_video_fallback": {"url": "", "id": "", "size": ""}, "background_slideshow_gallery": [], "background_slideshow_loop": "yes", "background_slideshow_slide_duration": 5000, "background_slideshow_slide_transition": "fade", "background_slideshow_transition_duration": 500, "background_slideshow_background_size": "", "background_slideshow_background_size_tablet": "", "background_slideshow_background_size_mobile": "", "background_slideshow_background_position": "", "background_slideshow_background_position_tablet": "", "background_slideshow_background_position_mobile": "", "background_slideshow_lazyload": "", "background_slideshow_ken_burns": "", "background_slideshow_ken_burns_zoom_direction": "in", "handle_slideshow_asset_loading": "", "background_hover_background": "", "background_hover_color": "", "background_hover_color_stop": {"unit": "%", "size": 0, "sizes": []}, "background_hover_color_stop_tablet": {"unit": "%"}, "background_hover_color_stop_mobile": {"unit": "%"}, "background_hover_color_b": "#f2295b", "background_hover_color_b_stop": {"unit": "%", "size": 100, "sizes": []}, "background_hover_color_b_stop_tablet": {"unit": "%"}, "background_hover_color_b_stop_mobile": {"unit": "%"}, "background_hover_gradient_type": "linear", "background_hover_gradient_angle": {"unit": "deg", "size": 180, "sizes": []}, "background_hover_gradient_angle_tablet": {"unit": "deg"}, "background_hover_gradient_angle_mobile": {"unit": "deg"}, "background_hover_gradient_position": "center center", "background_hover_gradient_position_tablet": "", "background_hover_gradient_position_mobile": "", "background_hover_image": {"url": "", "id": "", "size": ""}, "background_hover_image_tablet": {"url": "", "id": "", "size": ""}, "background_hover_image_mobile": {"url": "", "id": "", "size": ""}, "background_hover_position": "", "background_hover_position_tablet": "", "background_hover_position_mobile": "", "background_hover_xpos": {"unit": "px", "size": 0, "sizes": []}, "background_hover_xpos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_hover_xpos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_hover_ypos": {"unit": "px", "size": 0, "sizes": []}, "background_hover_ypos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_hover_ypos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_hover_attachment": "", "background_hover_repeat": "", "background_hover_repeat_tablet": "", "background_hover_repeat_mobile": "", "background_hover_size": "", "background_hover_size_tablet": "", "background_hover_size_mobile": "", "background_hover_bg_width": {"unit": "%", "size": 100, "sizes": []}, "background_hover_bg_width_tablet": {"unit": "px", "size": "", "sizes": []}, "background_hover_bg_width_mobile": {"unit": "px", "size": "", "sizes": []}, "background_hover_video_link": "", "background_hover_video_start": "", "background_hover_video_end": "", "background_hover_play_once": "", "background_hover_play_on_mobile": "", "background_hover_privacy_mode": "", "background_hover_video_fallback": {"url": "", "id": "", "size": ""}, "background_hover_slideshow_gallery": [], "background_hover_slideshow_loop": "yes", "background_hover_slideshow_slide_duration": 5000, "background_hover_slideshow_slide_transition": "fade", "background_hover_slideshow_transition_duration": 500, "background_hover_slideshow_background_size": "", "background_hover_slideshow_background_size_tablet": "", "background_hover_slideshow_background_size_mobile": "", "background_hover_slideshow_background_position": "", "background_hover_slideshow_background_position_tablet": "", "background_hover_slideshow_background_position_mobile": "", "background_hover_slideshow_lazyload": "", "background_hover_slideshow_ken_burns": "", "background_hover_slideshow_ken_burns_zoom_direction": "in", "background_hover_transition": {"unit": "px", "size": 0.3, "sizes": []}, "background_motion_fx_motion_fx_scrolling": "", "background_motion_fx_translateY_effect": "", "background_motion_fx_translateY_direction": "", "background_motion_fx_translateY_speed": {"unit": "px", "size": 4, "sizes": []}, "background_motion_fx_translateY_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "background_motion_fx_translateX_effect": "yes", "background_motion_fx_translateX_direction": "", "background_motion_fx_translateX_speed": {"unit": "px", "size": 10, "sizes": []}, "background_motion_fx_translateX_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "background_motion_fx_opacity_effect": "", "background_motion_fx_opacity_direction": "out-in", "background_motion_fx_opacity_level": {"unit": "px", "size": 10, "sizes": []}, "background_motion_fx_opacity_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "background_motion_fx_blur_effect": "", "background_motion_fx_blur_direction": "out-in", "background_motion_fx_blur_level": {"unit": "px", "size": 7, "sizes": []}, "background_motion_fx_blur_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "background_motion_fx_rotateZ_direction": "", "background_motion_fx_rotateZ_speed": {"unit": "px", "size": 1, "sizes": []}, "background_motion_fx_rotateZ_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "background_motion_fx_scale_effect": "", "background_motion_fx_scale_direction": "out-in", "background_motion_fx_scale_speed": {"unit": "px", "size": 4, "sizes": []}, "background_motion_fx_scale_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "background_motion_fx_devices": ["desktop", "mobile"], "background_motion_fx_range": "viewport", "background_motion_fx_motion_fx_mouse": "", "background_motion_fx_mouseTrack_effect": "", "background_motion_fx_mouseTrack_direction": "", "background_motion_fx_mouseTrack_speed": {"unit": "px", "size": 1, "sizes": []}, "background_motion_fx_tilt_direction": "", "background_motion_fx_tilt_speed": {"unit": "px", "size": 4, "sizes": []}, "background_handle_motion_fx_asset_loading": "", "background_overlay_background": "", "background_overlay_color": "", "background_overlay_color_stop": {"unit": "%", "size": 0, "sizes": []}, "background_overlay_color_stop_tablet": {"unit": "%"}, "background_overlay_color_stop_mobile": {"unit": "%"}, "background_overlay_color_b": "#f2295b", "background_overlay_color_b_stop": {"unit": "%", "size": 100, "sizes": []}, "background_overlay_color_b_stop_tablet": {"unit": "%"}, "background_overlay_color_b_stop_mobile": {"unit": "%"}, "background_overlay_gradient_type": "linear", "background_overlay_gradient_angle": {"unit": "deg", "size": 180, "sizes": []}, "background_overlay_gradient_angle_tablet": {"unit": "deg"}, "background_overlay_gradient_angle_mobile": {"unit": "deg"}, "background_overlay_gradient_position": "center center", "background_overlay_gradient_position_tablet": "", "background_overlay_gradient_position_mobile": "", "background_overlay_image": {"url": "", "id": "", "size": ""}, "background_overlay_image_tablet": {"url": "", "id": "", "size": ""}, "background_overlay_image_mobile": {"url": "", "id": "", "size": ""}, "background_overlay_position": "", "background_overlay_position_tablet": "", "background_overlay_position_mobile": "", "background_overlay_xpos": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_xpos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_xpos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_ypos": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_ypos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_ypos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_attachment": "", "background_overlay_repeat": "", "background_overlay_repeat_tablet": "", "background_overlay_repeat_mobile": "", "background_overlay_size": "", "background_overlay_size_tablet": "", "background_overlay_size_mobile": "", "background_overlay_bg_width": {"unit": "%", "size": 100, "sizes": []}, "background_overlay_bg_width_tablet": {"unit": "px", "size": "", "sizes": []}, "background_overlay_bg_width_mobile": {"unit": "px", "size": "", "sizes": []}, "background_overlay_video_link": "", "background_overlay_video_start": "", "background_overlay_video_end": "", "background_overlay_play_once": "", "background_overlay_play_on_mobile": "", "background_overlay_privacy_mode": "", "background_overlay_video_fallback": {"url": "", "id": "", "size": ""}, "background_overlay_slideshow_gallery": [], "background_overlay_slideshow_loop": "yes", "background_overlay_slideshow_slide_duration": 5000, "background_overlay_slideshow_slide_transition": "fade", "background_overlay_slideshow_transition_duration": 500, "background_overlay_slideshow_background_size": "", "background_overlay_slideshow_background_size_tablet": "", "background_overlay_slideshow_background_size_mobile": "", "background_overlay_slideshow_background_position": "", "background_overlay_slideshow_background_position_tablet": "", "background_overlay_slideshow_background_position_mobile": "", "background_overlay_slideshow_lazyload": "", "background_overlay_slideshow_ken_burns": "", "background_overlay_slideshow_ken_burns_zoom_direction": "in", "background_overlay_opacity": {"unit": "px", "size": 0.5, "sizes": []}, "background_overlay_opacity_tablet": {"unit": "px", "size": "", "sizes": []}, "background_overlay_opacity_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "", "css_filters_blur": {"unit": "px", "size": 0, "sizes": []}, "css_filters_brightness": {"unit": "px", "size": 100, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 100, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 100, "sizes": []}, "css_filters_hue": {"unit": "px", "size": 0, "sizes": []}, "overlay_blend_mode": "", "background_overlay_hover_background": "", "background_overlay_hover_color": "", "background_overlay_hover_color_stop": {"unit": "%", "size": 0, "sizes": []}, "background_overlay_hover_color_stop_tablet": {"unit": "%"}, "background_overlay_hover_color_stop_mobile": {"unit": "%"}, "background_overlay_hover_color_b": "#f2295b", "background_overlay_hover_color_b_stop": {"unit": "%", "size": 100, "sizes": []}, "background_overlay_hover_color_b_stop_tablet": {"unit": "%"}, "background_overlay_hover_color_b_stop_mobile": {"unit": "%"}, "background_overlay_hover_gradient_type": "linear", "background_overlay_hover_gradient_angle": {"unit": "deg", "size": 180, "sizes": []}, "background_overlay_hover_gradient_angle_tablet": {"unit": "deg"}, "background_overlay_hover_gradient_angle_mobile": {"unit": "deg"}, "background_overlay_hover_gradient_position": "center center", "background_overlay_hover_gradient_position_tablet": "", "background_overlay_hover_gradient_position_mobile": "", "background_overlay_hover_image": {"url": "", "id": "", "size": ""}, "background_overlay_hover_image_tablet": {"url": "", "id": "", "size": ""}, "background_overlay_hover_image_mobile": {"url": "", "id": "", "size": ""}, "background_overlay_hover_position": "", "background_overlay_hover_position_tablet": "", "background_overlay_hover_position_mobile": "", "background_overlay_hover_xpos": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_hover_xpos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_hover_xpos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_hover_ypos": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_hover_ypos_tablet": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_hover_ypos_mobile": {"unit": "px", "size": 0, "sizes": []}, "background_overlay_hover_attachment": "", "background_overlay_hover_repeat": "", "background_overlay_hover_repeat_tablet": "", "background_overlay_hover_repeat_mobile": "", "background_overlay_hover_size": "", "background_overlay_hover_size_tablet": "", "background_overlay_hover_size_mobile": "", "background_overlay_hover_bg_width": {"unit": "%", "size": 100, "sizes": []}, "background_overlay_hover_bg_width_tablet": {"unit": "px", "size": "", "sizes": []}, "background_overlay_hover_bg_width_mobile": {"unit": "px", "size": "", "sizes": []}, "background_overlay_hover_video_link": "", "background_overlay_hover_video_start": "", "background_overlay_hover_video_end": "", "background_overlay_hover_play_once": "", "background_overlay_hover_play_on_mobile": "", "background_overlay_hover_privacy_mode": "", "background_overlay_hover_video_fallback": {"url": "", "id": "", "size": ""}, "background_overlay_hover_slideshow_gallery": [], "background_overlay_hover_slideshow_loop": "yes", "background_overlay_hover_slideshow_slide_duration": 5000, "background_overlay_hover_slideshow_slide_transition": "fade", "background_overlay_hover_slideshow_transition_duration": 500, "background_overlay_hover_slideshow_background_size": "", "background_overlay_hover_slideshow_background_size_tablet": "", "background_overlay_hover_slideshow_background_size_mobile": "", "background_overlay_hover_slideshow_background_position": "", "background_overlay_hover_slideshow_background_position_tablet": "", "background_overlay_hover_slideshow_background_position_mobile": "", "background_overlay_hover_slideshow_lazyload": "", "background_overlay_hover_slideshow_ken_burns": "", "background_overlay_hover_slideshow_ken_burns_zoom_direction": "in", "background_overlay_hover_opacity": {"unit": "px", "size": 0.5, "sizes": []}, "background_overlay_hover_opacity_tablet": {"unit": "px", "size": "", "sizes": []}, "background_overlay_hover_opacity_mobile": {"unit": "px", "size": "", "sizes": []}, "background_overlay_hover_transition": {"unit": "px", "size": "", "sizes": []}, "css_filters_hover_css_filter": "", "css_filters_hover_blur": {"unit": "px", "size": 0, "sizes": []}, "css_filters_hover_brightness": {"unit": "px", "size": 100, "sizes": []}, "css_filters_hover_contrast": {"unit": "px", "size": 100, "sizes": []}, "css_filters_hover_saturate": {"unit": "px", "size": 100, "sizes": []}, "css_filters_hover_hue": {"unit": "px", "size": 0, "sizes": []}, "border_border": "", "border_width": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_width_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_width_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_color": "", "border_radius": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_radius_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_radius_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "box_shadow_box_shadow_type": "", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(0,0,0,0.5)"}, "box_shadow_box_shadow_position": " ", "border_hover_border": "", "border_hover_width": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_hover_width_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_hover_width_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_hover_color": "", "border_radius_hover": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_radius_hover_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "border_radius_hover_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "box_shadow_hover_box_shadow_type": "", "box_shadow_hover_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(0,0,0,0.5)"}, "box_shadow_hover_box_shadow_position": " ", "border_hover_transition": {"unit": "px", "size": 0.3, "sizes": []}, "shape_divider_top": "", "shape_divider_top_color": "", "shape_divider_top_width": {"unit": "%", "size": "", "sizes": []}, "shape_divider_top_width_tablet": {"unit": "%", "size": "", "sizes": []}, "shape_divider_top_width_mobile": {"unit": "%", "size": "", "sizes": []}, "shape_divider_top_height": {"unit": "px", "size": "", "sizes": []}, "shape_divider_top_height_tablet": {"unit": "px", "size": "", "sizes": []}, "shape_divider_top_height_mobile": {"unit": "px", "size": 500, "sizes": []}, "shape_divider_top_flip": "", "shape_divider_top_negative": "yes", "shape_divider_top_above_content": "", "shape_divider_bottom": "", "shape_divider_bottom_color": "", "shape_divider_bottom_width": {"unit": "%", "size": "", "sizes": []}, "shape_divider_bottom_width_tablet": {"unit": "%", "size": "", "sizes": []}, "shape_divider_bottom_width_mobile": {"unit": "%", "size": "", "sizes": []}, "shape_divider_bottom_height": {"unit": "px", "size": "", "sizes": []}, "shape_divider_bottom_height_tablet": {"unit": "px", "size": "", "sizes": []}, "shape_divider_bottom_height_mobile": {"unit": "px", "size": 75, "sizes": []}, "shape_divider_bottom_flip": "", "shape_divider_bottom_negative": "", "shape_divider_bottom_above_content": "", "margin": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "margin_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "margin_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "padding": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "padding_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "padding_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "grid_column": "", "grid_column_tablet": "", "grid_column_mobile": "", "grid_column_custom": "", "grid_column_custom_tablet": "", "grid_column_custom_mobile": "", "grid_row": "", "grid_row_tablet": "", "grid_row_mobile": "", "grid_row_custom": "", "grid_row_custom_tablet": "", "grid_row_custom_mobile": "", "_flex_align_self": "", "_flex_align_self_tablet": "", "_flex_align_self_mobile": "", "_flex_order": "", "_flex_order_tablet": "", "_flex_order_mobile": "", "_flex_order_custom": "", "_flex_order_custom_tablet": "", "_flex_order_custom_mobile": "", "_flex_size": "", "_flex_size_tablet": "", "_flex_size_mobile": "", "_flex_grow": 1, "_flex_grow_tablet": "", "_flex_grow_mobile": "", "_flex_shrink": 1, "_flex_shrink_tablet": "", "_flex_shrink_mobile": "", "position": "", "_offset_orientation_h": "start", "_offset_x": {"unit": "px", "size": 0, "sizes": []}, "_offset_x_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_x_mobile": {"unit": "px", "size": "", "sizes": []}, "_offset_x_end": {"unit": "px", "size": 0, "sizes": []}, "_offset_x_end_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_x_end_mobile": {"unit": "px", "size": "", "sizes": []}, "_offset_orientation_v": "start", "_offset_y": {"unit": "px", "size": 0, "sizes": []}, "_offset_y_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_y_mobile": {"unit": "px", "size": "", "sizes": []}, "_offset_y_end": {"unit": "px", "size": 0, "sizes": []}, "_offset_y_end_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_y_end_mobile": {"unit": "px", "size": "", "sizes": []}, "z_index": "", "z_index_tablet": "", "z_index_mobile": "", "_element_id": "source-container", "css_classes": "", "e_display_conditions": "", "motion_fx_motion_fx_scrolling": "", "motion_fx_translateY_effect": "", "motion_fx_translateY_direction": "", "motion_fx_translateY_speed": {"unit": "px", "size": 4, "sizes": []}, "motion_fx_translateY_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "motion_fx_translateX_effect": "", "motion_fx_translateX_direction": "", "motion_fx_translateX_speed": {"unit": "px", "size": 4, "sizes": []}, "motion_fx_translateX_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "motion_fx_opacity_effect": "", "motion_fx_opacity_direction": "out-in", "motion_fx_opacity_level": {"unit": "px", "size": 10, "sizes": []}, "motion_fx_opacity_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "motion_fx_blur_effect": "", "motion_fx_blur_direction": "out-in", "motion_fx_blur_level": {"unit": "px", "size": 7, "sizes": []}, "motion_fx_blur_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "motion_fx_rotateZ_effect": "", "motion_fx_rotateZ_direction": "", "motion_fx_rotateZ_speed": {"unit": "px", "size": 1, "sizes": []}, "motion_fx_rotateZ_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "motion_fx_scale_effect": "", "motion_fx_scale_direction": "out-in", "motion_fx_scale_speed": {"unit": "px", "size": 4, "sizes": []}, "motion_fx_scale_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "motion_fx_transform_origin_x": "center", "motion_fx_transform_origin_y": "center", "motion_fx_devices": ["desktop", "tablet", "mobile"], "motion_fx_range": "", "motion_fx_motion_fx_mouse": "", "motion_fx_mouseTrack_effect": "", "motion_fx_mouseTrack_direction": "", "motion_fx_mouseTrack_speed": {"unit": "px", "size": 1, "sizes": []}, "motion_fx_tilt_effect": "", "motion_fx_tilt_direction": "", "motion_fx_tilt_speed": {"unit": "px", "size": 4, "sizes": []}, "handle_motion_fx_asset_loading": "", "sticky": "", "sticky_on": ["desktop", "tablet", "mobile"], "sticky_offset": 0, "sticky_offset_tablet": "", "sticky_offset_mobile": "", "sticky_effects_offset": 0, "sticky_effects_offset_tablet": "", "sticky_effects_offset_mobile": "", "sticky_anchor_link_offset": 0, "sticky_anchor_link_offset_tablet": "", "sticky_anchor_link_offset_mobile": "", "sticky_parent": "", "animation": "", "animation_tablet": "", "animation_mobile": "", "animation_duration": "", "animation_delay": "", "_transform_rotate_popover": "", "_transform_rotateZ_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateZ_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateZ_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotate_3d": "", "_transform_rotateX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateX_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateX_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateY_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_perspective_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translate_popover": "", "_transform_translateX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_popover": "", "_transform_keep_proportions": "yes", "_transform_scale_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_skew_popover": "", "_transform_skewX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_skewX_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewX_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_skewY_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_flipX_effect": "", "_transform_flipY_effect": "", "_transform_rotate_popover_hover": "", "_transform_rotateZ_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateZ_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateZ_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotate_3d_hover": "", "_transform_rotateX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateX_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateX_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateY_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_perspective_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translate_popover_hover": "", "_transform_translateX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_popover_hover": "", "_transform_keep_proportions_hover": "yes", "_transform_scale_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_skew_popover_hover": "", "_transform_skewX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_skewX_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewX_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_skewY_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_flipX_effect_hover": "", "_transform_flipY_effect_hover": "", "_transform_transition_hover": {"unit": "px", "size": "", "sizes": []}, "motion_fx_transform_x_anchor_point": "", "motion_fx_transform_x_anchor_point_tablet": "", "motion_fx_transform_x_anchor_point_mobile": "", "motion_fx_transform_y_anchor_point": "", "motion_fx_transform_y_anchor_point_tablet": "", "motion_fx_transform_y_anchor_point_mobile": "", "hide_desktop": "", "hide_tablet": "", "hide_mobile": "", "custom_css": ""}, "defaultEditSettings": {"defaultEditRoute": "content"}, "elements": [], "editSettings": {"defaultEditRoute": "layout", "panel": {"activeTab": "advanced", "activeSection": "_section_attributes"}}}]}