# Madam [[Doing/madam/Readme|Readme]]
## Documentación de la tienda de Madam Supply

### [[Información de la instalación de Wordpress]] 

### [[Productos y Categorías]]

### [[Assets Madam]]

### [[E-commerce Image Optimization Guide for Fashion Accessories]]

### [[docs/elementor-pro-widgets-technical-docs.md|Documentación Técnica: Widgets de Elementor Pro con Swiper.js y FlexSlider]]

### Credenciales de Acceso

*   **URL:** https://madam.supply/wp-admin
*   **Usuario:** Supply
*   **Contraseña:** 3fc)d4JbZ6DBaVO*suX&RYV2

## Descripción

El sitio está implementado en Wordpress con Woocomerce, para el diseño se usarán, en la medida de lo posible, los widgets nativos de Elementor, de los plugins instalados, como woocommerce y se usarán widgets HTML para funcionalidades que no sea posible utilizando las carcateristicas nativas de las herramientas instaladas en wordpress.

Se usará la funcionalidad de [[Doing/madam/Online Store/Custom Code de Elementor Pro]] para agregar el código adicional estrictamente necesario y de forma óptima para mantener en performance de la tienda.

Se usar podrá la funcionalidad de CSS personalizado de los templates, páginas o widgets de Elementor en los casos en que dicha implementación sea más óptima para asegurar el funcionamiento de la característica que se debe desarrollar y el performance de la página.

## Jerarquía de la importancia en el desarrollo general de la tienda

1. Performance para presentar el contenido al usuario y UX
2. Performance del SEO y SEM
3. Diseño alineado con la marca

## Estructura del directorio del proyecto

Este proyecto está organizado de la siguiente manera:

-   **Archivos Markdown Principales (en la raíz):**
    -   `Readme.md`: Este archivo, documentación principal del proyecto.
    -   `Custom Code de Elementor Pro.md`: Detalles sobre el uso de la funcionalidad de código personalizado de Elementor Pro.
    -   `Información de la instalación de Wordpress.md`: Detalles técnicos de la configuración de WordPress, incluyendo plugins y tema.
    -   `Productos y Categorías.md`: Información sobre el inventario de productos, estructura de categorías en WooCommerce y análisis de marketing.
    -   `E-commerce Image Optimization Guide for Fashion Accessories.md`: Guía completa de optimización de imágenes para e-commerce de accesorios de moda, incluyendo especificaciones técnicas, SEO, requisitos de plataforma y experiencia de usuario.
-   `assets/`: Contiene todos los activos visuales del proyecto.
    -   `Assets Madam.md`: Documentación específica sobre los assets, como el logo.
    -   `1x/`: Imágenes de productos y modelos en resolución estándar.
    -   `SVG/`: Archivos SVG, incluyendo el favicon y versiones del logo.
    -   También incluye otros archivos de imagen (JPG, WEBP, AI) directamente en la carpeta `assets/`.
-   `custom-code/`: Almacena fragmentos de código HTML, CSS y JavaScript personalizados que se integran mediante la funcionalidad "Custom Code" de Elementor Pro.
    -   `madam-footer-home-custom-code.html`: Código personalizado para el pie de página de la home.
    -   `madam-header-home-custom-code.html`: Código personalizado para el encabezado de la home.
    -   `madam-carousel-stepless-custom-code.html`: Solución limpia para carousels con altura uniforme y efectos fade.
-   `elements/`: Contiene exportaciones JSON de elementos, plantillas o secciones creadas con Elementor. Estos archivos permiten hacer copias de seguridad o importar diseños en otras partes del sitio o en otros proyectos de Elementor.
    -   `foto-completa-modelos-sentados.json`: Elemento de Elementor con una vista completa de modelos sentados.
    -   `header.json`: Plantilla del encabezado del sitio.
    -   `madam-footer-home.json`: Plantilla del pie de página de la página principal.
    -   `shop.json`: Plantilla de la página de tienda.
-   `scripts/`: Contiene scripts automatizados para optimización y procesamiento de assets del proyecto.
    -   `Convert-JPGToAVIF.ps1`: Script de PowerShell para conversión en lote de imágenes JPG al formato AVIF con redimensionamiento automático y optimización para web.
    -   `README.md`: Documentación completa de los scripts disponibles, parámetros, uso y mejores prácticas.

## Custom Code implementado

### Header Color Responsive

Ubicación: `custom-code/madam-header-home-custom-code.html`

Este código CSS aplica el background-color global `--e-global-color-9ddb383` a la clase `.madam-header-home` solamente en dispositivos tablet y escritorio (pantallas de 768px o más). Se implementó usando la funcionalidad de Custom Code de Elementor Pro para mantener una adecuada separación de responsabilidades y optimizar el performance del sitio.

### Footer Gradient y Logo Responsive

Ubicación: `custom-code/madam-footer-home-custom-code.html`

Este código JavaScript implementa un footer con degradado y logo adaptable. Sus principales características son:

1. **Degradado Responsivo**:
   - Aplica un degradado desde transparente hasta un color base configurable
   - La altura del degradado se ajusta según el dispositivo (móvil, tablet, escritorio)
   - El color base se aplica como fondo sólido en la parte inferior del footer

2. **Logo Adaptable**:
   - Permite cambiar dinámicamente el color del logo SVG
   - Implementa tres métodos de coloración para máxima compatibilidad:
     - Manipulación directa de paths SVG
     - Estilos CSS específicos
     - Filtros CSS para imágenes

3. **Menú Personalizable**:
   - Permite configurar el color del texto del menú del footer

4. **Configuración Responsiva**:
   - Los valores se pueden especificar por dispositivo usando sufijos (-mobile, -tablet, -desktop)
   - Se actualiza automáticamente al cambiar el tamaño de la ventana

El código utiliza atributos data- para su configuración:
- `data-gradient-height`: Altura del degradado
- `data-gradient-color`: Color base del degradado (default: #C4B9AF)
- `data-gradient-direction`: Dirección del degradado (default: to bottom)
- `data-logo-color`: Color del logo SVG
- `data-menu-color`: Color del texto del menú

Se implementó usando la funcionalidad de Custom Code de Elementor Pro para mantener una adecuada separación de responsabilidades y optimizar el performance del sitio.

### Carousel de Imágenes con Relación de Aspecto Correcta y Altura Uniforme + Efectos Fade

Ubicación: `custom-code/madam-carousel-stepless-custom-code.html`


## Características Principales:

1. **Altura Uniforme + Efecto Fade**:
   - Todas las imágenes mantienen la misma altura independientemente de su relación de aspecto
   - **Fade del carousel completo**: Aparición suave (0.6s) al cargar la página
   - **Fade progresivo de imágenes**: Cada imagen aparece con fade individual (0.4s) en secuencia
   - Utiliza `object-fit: cover` para mantener la relación de aspecto sin distorsión

2. **Sistema Responsivo**:
   - **Desktop**: 55vh (55% de la altura de la ventana)
   - **Tablet**: 40vh (40% de la altura de la ventana)  
   - **Mobile**: 35vh (35% de la altura de la ventana)

3. **Tecnologías Utilizadas**:
   - `object-fit: cover` - Mantiene relación de aspecto sin distorsión
   - `object-position: center center` - Centra la imagen perfectamente
   - Viewport units (vh) para responsividad fluida
   - Transiciones CSS con `opacity` para efectos fade
   - JavaScript optimizado para manejo de carga de imágenes

4. **Funcionalidades JavaScript**:
   - **Lazy loading automático**: Optimización de carga de imágenes
   - **Error handling**: Manejo robusto de errores de imagen
   - **Fade escalonado**: Cada imagen aparece con 100ms de diferencia
   - **Detección de imágenes cargadas**: Funciona tanto con imágenes ya cargadas como en carga

**Implementación Rápida**:
1. Agregar la clase CSS `stepless-ratio-carousel` al widget de carousel en Elementor (Advanced → CSS Classes)
2. Copiar todo el contenido de `madam-carousel-stepless-custom-code.html`
3. Pegar en un widget HTML de la misma página O en Elementor → Custom Code

**Personalización de Alturas**:
```css
/* Para cambiar altura desktop */
.stepless-ratio-carousel .swiper-slide-image {
    height: 60vh !important; /* Tu valor preferido */
}
```

**Funciones Adicionales Disponibles**:
- `fadeOutCarousel(carousel)` - Fade out manual del carousel
- `resetCarouselFade(carousel)` - Reset y refresh del carousel

**Mejoras Opcionales Incluidas** (comentadas, actívables):
- Efecto hover suave con escala
- Sombras sutiles para mayor profundidad

**Ventajas**:
- **Código limpio**: Eliminado código complejo innecesario
- **Efectos visuales**: Fade suave para mejor UX
- **Performance optimizado**: Solo el código esencial
- **Fácil implementación**: Una sola clase CSS necesaria
- **Manejo de errores**: Robusto ante fallos de carga de imágenes
- **Compatibilidad total**: Funciona con todas las funcionalidades de Elementor

Esta solución es perfecta para galerías de producto, portfolios, y cualquier carousel donde se necesite presentación visual uniforme con una experiencia de carga suave y profesional.

## Documentación de Bibliotecas de Sliders

Para un entendimiento detallado de las bibliotecas de sliders utilizadas en este proyecto (Swiper.js para Elementor Pro y FlexSlider para WooCommerce), consulta el siguiente documento:

-   [Documentación: Swiper.js en Elementor Pro y FlexSlider en WooCommerce](docs/swiper-flexslider-docs.md)

