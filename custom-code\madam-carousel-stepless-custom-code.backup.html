<!-- 
CAROUSEL STEPLESS RESPONSIVO - PRODUCCIÓN
Compatible con Elementor Pro 3.29.0 + Swiper 8
✅ Desktop: Movimiento stepless continuo
✅ Tablet/Móvil: Comportamiento normal de Elementor
-->

<style>
/* ===================================
   CSS OPTIMIZADO - RESPONSIVO INTELIGENTE
   ================================= */

.stepless-ratio-carousel .swiper-wrapper {
    max-height: 52vh;
    align-items: center;
    
    /* Configuración base para todos los dispositivos */
    will-change: transform;
    transform: translate3d(0, 0, 0);
}

/* CONFIGURACIÓN ESPECÍFICA PARA DESKTOP (1025px+) */
@media (min-width: 1025px) {
    .stepless-ratio-carousel.desktop-stepless .swiper-wrapper {
        /* SOLO en desktop: transición linear para movimiento stepless */
        transition-timing-function: linear !important;
        -webkit-transition-timing-function: linear !important;
        -o-transition-timing-function: linear !important;
    }
}

.stepless-ratio-carousel .swiper-slide {
    width: auto !important;
    height: 100%;
    display: flex;
    align-items: center;
    flex: 0 0 auto !important;
    
    /* Mantener clickeabilidad total */
    pointer-events: all !important;
    cursor: pointer;
    position: relative;
    z-index: 2;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
}

.stepless-ratio-carousel .swiper-slide img {
    max-height: 52.5vh;
    width: auto;
    object-fit: cover;
    transform: translate3d(0, 0, 0);
    pointer-events: all !important;
    cursor: pointer;
    position: relative;
    z-index: 5;
}

/* Enlaces y elementos clickeables */
.stepless-ratio-carousel a,
.stepless-ratio-carousel a img,
.stepless-ratio-carousel .elementor-image,
.stepless-ratio-carousel .elementor-widget-container {
    pointer-events: all !important;
    position: relative;
    z-index: 5;
    cursor: pointer;
}

/* Slides duplicados */
.stepless-ratio-carousel .swiper-slide-duplicate,
.stepless-ratio-carousel .swiper-slide[aria-hidden="true"] {
    pointer-events: all !important;
    cursor: pointer;
    z-index: 2;
}

.stepless-ratio-carousel .swiper-slide-duplicate *,
.stepless-ratio-carousel .swiper-slide[aria-hidden="true"] * {
    pointer-events: all !important;
    z-index: 5;
    cursor: pointer !important;
}

/* RESPONSIVE - TABLET */
@media (max-width: 1024px) and (min-width: 769px) {
    .stepless-ratio-carousel .swiper-wrapper {
        max-height: 45vh;
        /* NO aplicar transición linear - comportamiento normal */
    }
    .stepless-ratio-carousel .swiper-slide img {
        max-height: 45vh;
    }
}

/* RESPONSIVE - MÓVIL */
@media (max-width: 768px) {
    .stepless-ratio-carousel .swiper-wrapper {
        max-height: 40vh;
        /* NO aplicar transición linear - comportamiento normal */
    }
    .stepless-ratio-carousel .swiper-slide img {
        max-height: 40vh;
    }
    .stepless-ratio-carousel {
        touch-action: pan-y;
    }
}

@media (max-width: 480px) {
    .stepless-ratio-carousel .swiper-wrapper {
        max-height: 52vh;
    }
    .stepless-ratio-carousel .swiper-slide img {
        max-height: 52vh;
    }
}

/* ACCESIBILIDAD */
@media (prefers-reduced-motion: reduce) {
    .stepless-ratio-carousel .swiper-wrapper {
        animation: none !important;
        transition: none !important;
    }
}

/* Configuración para entorno de producción */
</style>

<script>
/* ===================================
   JAVASCRIPT INTELIGENTE - DESKTOP ONLY
   ================================= */

document.addEventListener('DOMContentLoaded', function() {
    // DETECCIÓN DE DISPOSITIVO
    function isDesktopDevice() {
        const screenWidth = window.innerWidth;
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        const userAgent = navigator.userAgent.toLowerCase();
        
        // Criterios para desktop:
        const hasMinWidth = screenWidth >= 1025; // Mínimo 1025px
        const isNotMobile = !(/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent));
        const isNotTouch = !isTouchDevice;
        
        // Desktop = ancho suficiente Y (no móvil O no touch)
        return hasMinWidth && (isNotMobile || !isNotTouch);
    }
      // FUNCIÓN PRINCIPAL
    function initializeSmartCarousels() {
        const carousels = document.querySelectorAll('.stepless-ratio-carousel');
        const isDesktop = isDesktopDevice();
        
        if (carousels.length === 0) {
            setTimeout(initializeSmartCarousels, 1000);
            return;
        }
        
        carousels.forEach(function(carousel, index) {
            if (isDesktop) {
                // SOLO EN DESKTOP: Aplicar funcionalidad stepless
                carousel.classList.add('desktop-stepless');
                setupSteplessCarousel(carousel, index);
            } else {
                // EN MÓVIL/TABLET: Solo configurar clickeabilidad básica
                carousel.classList.remove('desktop-stepless');
                setupBasicClickability(carousel, index);
            }
        });
        
        // Observer para cambios dinámicos
        observeCarouselChanges(carousels, isDesktop);
    }
    
    // CONFIGURACIÓN STEPLESS PARA DESKTOP    function setupSteplessCarousel(carousel, index) {
        const swiperContainer = carousel.querySelector('.swiper');
        
        if (swiperContainer && swiperContainer.swiper) {
            const swiper = swiperContainer.swiper;
            
            // DETENER completamente el autoplay actual
            swiper.autoplay.stop();
            swiper.autoplay.pause();
            
            // CONFIGURACIÓN STEPLESS ESPECÍFICA
            Object.assign(swiper.params, {
                autoplay: {
                    delay: 0,                      // Sin pausa
                    disableOnInteraction: false,   // Continuar tras interacción
                    pauseOnMouseEnter: false,      // No pausar en hover
                    reverseDirection: false,       
                    stopOnLastSlide: false         
                },
                speed: 8000,                       // Velocidad moderada
                loop: true,                        
                slidesPerView: 'auto',            
                
                allowTouchMove: true,              
                simulateTouch: true,               
                touchRatio: 0.1,                   // Touch muy reducido en desktop
                resistance: true,                  
                resistanceRatio: 0.1,              
                
                watchSlidesProgress: true,         
                centeredSlides: false,             
            });
            
            // Aplicar configuración de autoplay específicamente
            Object.assign(swiper.params.autoplay, {
                delay: 0,
                disableOnInteraction: false,
                pauseOnMouseEnter: false
            });
            
            // CSS para linearidad (SOLO desktop)
            const wrapper = swiperContainer.querySelector('.swiper-wrapper');
            if (wrapper) {
                wrapper.style.transitionTimingFunction = 'linear';
                wrapper.style.webkitTransitionTimingFunction = 'linear';
            }
            
            // Configurar clickeabilidad
            configureSlideClickability(swiperContainer);
            
            // REINICIAR COMPLETAMENTE
            setTimeout(function() {
                swiper.autoplay.stop();
                setTimeout(function() {
                    swiper.autoplay.start();
                }, 300);
            }, 500);
            
            // MONITOR ESPECÍFICO PARA STEPLESS
            setupSteplessMonitor(swiper, index);
        }
    }
    
    // CONFIGURACIÓN BÁSICA PARA MÓVIL/TABLET
    function setupBasicClickability(carousel, index) {
        console.log(`📱 Configurando BÁSICO en carousel ${index + 1}...`);
        
        const swiperContainer = carousel.querySelector('.swiper');
        if (swiperContainer) {
            // Solo configurar clickeabilidad, NO tocar autoplay
            configureSlideClickability(swiperContainer);
            
            // Asegurar que el autoplay de Elementor funcione normalmente
            if (swiperContainer.swiper) {
                const swiper = swiperContainer.swiper;
                
                // NO modificar parámetros de autoplay - dejar configuración de Elementor
                console.log(`✅ BÁSICO Carousel ${index + 1} configurado (Elementor nativo)`);
            }
        }
    }
    
    // MONITOR PARA STEPLESS (SOLO DESKTOP)
    function setupSteplessMonitor(swiper, index) {
        let consecutiveFailures = 0;
        const maxFailures = 3;
        
        const smartMonitor = setInterval(function() {
            if (!swiper.destroyed) {
                if (!swiper.autoplay.running && !swiper.autoplay.paused) {
                    consecutiveFailures++;
                    console.log(`⚠️ STEPLESS Carousel ${index + 1} detectado parado (${consecutiveFailures}/${maxFailures})`);
                    
                    if (consecutiveFailures >= maxFailures) {
                        console.log(`🔄 REINICIANDO STEPLESS carousel ${index + 1}`);
                        
                        swiper.autoplay.stop();
                        swiper.update();
                        
                        setTimeout(function() {
                            swiper.autoplay.start();
                            consecutiveFailures = 0;
                        }, 500);
                    }
                } else {
                    consecutiveFailures = 0;
                }
            } else {
                clearInterval(smartMonitor);
            }
        }, 20000);
        
        // LISTENERS ESPECÍFICOS PARA STEPLESS
        swiper.on('autoplayStop', function() {
            console.log(`⚠️ STEPLESS Autoplay detenido en carousel ${index + 1}`);
        });
        
        swiper.on('transitionEnd', function() {
            if (!swiper.autoplay.running && !swiper.destroyed) {
                setTimeout(function() {
                    swiper.autoplay.start();
                }, 100);
            }
        });
    }
    
    // CONFIGURAR CLICKEABILIDAD (COMÚN PARA TODOS)
    function configureSlideClickability(swiperContainer) {
        const allSlides = swiperContainer.querySelectorAll('.swiper-slide');
        
        allSlides.forEach(function(slide) {
            slide.style.pointerEvents = 'all';
            slide.style.cursor = 'pointer';
            
            const images = slide.querySelectorAll('img');
            images.forEach(function(img) {
                img.style.pointerEvents = 'all';
                img.style.cursor = 'pointer';
                
                if (!img.closest('a')) {
                    const link = document.createElement('a');
                    link.href = img.getAttribute('src') || img.getAttribute('data-src');
                    link.setAttribute('data-elementor-open-lightbox', 'yes');
                    link.setAttribute('data-elementor-lightbox-slideshow', 'gallery');
                    link.style.pointerEvents = 'all';
                    link.style.display = 'block';
                    
                    img.parentNode.insertBefore(link, img);
                    link.appendChild(img);
                }
            });
            
            const links = slide.querySelectorAll('a');
            links.forEach(function(link) {
                link.style.pointerEvents = 'all';
                if (!link.hasAttribute('data-elementor-open-lightbox')) {
                    link.setAttribute('data-elementor-open-lightbox', 'yes');
                    link.setAttribute('data-elementor-lightbox-slideshow', 'gallery');
                }
            });
        });
    }
    
    // OBSERVER INTELIGENTE
    function observeCarouselChanges(carousels, isDesktop) {
        carousels.forEach(function(carousel) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes.length) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && 
                                (node.classList.contains('swiper-slide') || 
                                 node.classList.contains('swiper-slide-duplicate'))) {
                                
                                node.style.pointerEvents = 'all';
                                node.style.cursor = 'pointer';
                                
                                const newImages = node.querySelectorAll('img:not(a img)');
                                newImages.forEach(function(img) {
                                    img.style.pointerEvents = 'all';
                                    if (!img.closest('a')) {
                                        const link = document.createElement('a');
                                        link.href = img.getAttribute('src');
                                        link.setAttribute('data-elementor-open-lightbox', 'yes');
                                        link.setAttribute('data-elementor-lightbox-slideshow', 'gallery');
                                        img.parentNode.insertBefore(link, img);
                                        link.appendChild(img);
                                    }
                                });
                            }
                        });
                    }
                });
            });
            
            observer.observe(carousel, { 
                childList: true, 
                subtree: true 
            });
        });
    }
    
    // DETECCIÓN DE CAMBIO DE TAMAÑO
    function handleResize() {
        const wasDesktop = document.querySelector('.stepless-ratio-carousel.desktop-stepless') !== null;
        const isDesktopNow = isDesktopDevice();
        
        if (wasDesktop !== isDesktopNow) {
            console.log(`📐 Cambio de dispositivo detectado: ${wasDesktop ? 'Desktop' : 'Móvil'} → ${isDesktopNow ? 'Desktop' : 'Móvil'}`);
            
            // Reinicializar todo
            setTimeout(function() {
                initializeSmartCarousels();
            }, 500);
        }
    }
    
    // LISTENER PARA CAMBIOS DE TAMAÑO
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResize, 300);
    });
    
    // FUNCIONES DE DEBUG MEJORADAS
    window.debugSteplessCarousel = function() {
        const carousels = document.querySelectorAll('.stepless-ratio-carousel');
        const isDesktop = isDesktopDevice();
        
        console.log('🔍 DEBUG STEPLESS CAROUSEL:');
        console.log(`Dispositivo actual: ${isDesktop ? 'DESKTOP' : 'MÓVIL/TABLET'}`);
        console.log(`Total carousels: ${carousels.length}`);
        console.log(`Carousels stepless: ${document.querySelectorAll('.stepless-ratio-carousel.desktop-stepless').length}`);
        
        carousels.forEach(function(carousel, index) {
            const isStepless = carousel.classList.contains('desktop-stepless');
            const swiper = carousel.querySelector('.swiper');
            
            console.log(`\n📊 CAROUSEL ${index + 1} (${isStepless ? 'STEPLESS' : 'BÁSICO'}):`);
            
            if (swiper && swiper.swiper) {
                const s = swiper.swiper;
                console.log('✓ Autoplay running:', s.autoplay.running);
                console.log('✓ Autoplay paused:', s.autoplay.paused);
                console.log('✓ Delay:', s.params.autoplay.delay);
                console.log('✓ Speed:', s.params.speed);
                console.log('✓ DisableOnInteraction:', s.params.autoplay.disableOnInteraction);
                
                if (s.autoplay.running && !s.autoplay.paused && !s.destroyed) {
                    console.log('🟢 ESTADO: SALUDABLE');
                } else {
                    console.log('🔴 ESTADO: PROBLEMÁTICO');
                }
            }
        });
    };
    
    window.toggleSteplessMode = function() {
        const carousels = document.querySelectorAll('.stepless-ratio-carousel');
        carousels.forEach(carousel => {
            if (carousel.classList.contains('desktop-stepless')) {
                carousel.classList.remove('desktop-stepless');
                console.log('🔄 Cambiado a modo BÁSICO');
            } else {
                carousel.classList.add('desktop-stepless');
                console.log('🔄 Cambiado a modo STEPLESS');
            }
        });
        
        setTimeout(() => initializeSmartCarousels(), 500);
    };
    
    // INICIALIZACIÓN PRINCIPAL
    setTimeout(function() {
        initializeSmartCarousels();
    }, 2500);
    
    console.log('✅ Stepless Carousel DESKTOP ONLY cargado');
    console.log('💡 Usa debugSteplessCarousel() para diagnóstico');
    console.log('💡 Usa toggleSteplessMode() para cambiar modo');
});
</script>