#!/usr/bin/env node

// SCRIPT PRINCIPAL DE DIAGNÓSTICO
// Ejecuta todos los análisis del carrusel móvil

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

const { analizarCarouselCSS } = require('./css-analyzer');
const { diagnosticarCarruselMovil } = require('./carousel-puppeteer-test');

class DiagnosticRunner {
    constructor() {
        this.resultados = {
            timestamp: new Date().toISOString(),
            diagnosticos: {}
        };
        
        this.crearDirectorios();
    }
    
    crearDirectorios() {
        const dirs = ['screenshots', 'test-results', 'reports'];
        dirs.forEach(dir => {
            const fullPath = path.join(__dirname, dir);
            if (!fs.existsSync(fullPath)) {
                fs.mkdirSync(fullPath, { recursive: true });
            }
        });
    }
    
    async ejecutarTodos() {
        console.log('🚀 INICIANDO DIAGNÓSTICO COMPLETO DEL CARRUSEL MÓVIL');
        console.log('=' .repeat(60));
        
        try {
            // 1. Análisis CSS
            console.log('\n📝 1. ANÁLISIS CSS...');
            await this.ejecutarAnalisisCSS();
            
            // 2. Diagnóstico con Puppeteer
            console.log('\n🤖 2. DIAGNÓSTICO CON PUPPETEER...');
            await this.ejecutarPuppeteer();
            
            // 3. Tests con Playwright (opcional, requiere configuración)
            console.log('\n🎭 3. TESTS CON PLAYWRIGHT...');
            await this.ejecutarPlaywright();
            
            // 4. Generar reporte consolidado
            console.log('\n📊 4. GENERANDO REPORTE CONSOLIDADO...');
            await this.generarReporteConsolidado();
            
            console.log('\n✅ DIAGNÓSTICO COMPLETADO EXITOSAMENTE');
            this.mostrarResumen();
            
        } catch (error) {
            console.error('❌ Error durante el diagnóstico:', error.message);
            throw error;
        }
    }
    
    async ejecutarAnalisisCSS() {
        try {
            const reporteCSS = await analizarCarouselCSS();
            this.resultados.diagnosticos.css = reporteCSS;
            console.log('✅ Análisis CSS completado');
        } catch (error) {
            console.error('❌ Error en análisis CSS:', error.message);
            this.resultados.diagnosticos.css = { error: error.message };
        }
    }
    
    async ejecutarPuppeteer() {
        try {
            const reportePuppeteer = await diagnosticarCarruselMovil();
            this.resultados.diagnosticos.puppeteer = reportePuppeteer;
            console.log('✅ Diagnóstico Puppeteer completado');
        } catch (error) {
            console.error('❌ Error en Puppeteer:', error.message);
            this.resultados.diagnosticos.puppeteer = { error: error.message };
        }
    }
    
    async ejecutarPlaywright() {
        try {
            // Verificar si Playwright está instalado
            const { stdout } = await execAsync('npx playwright --version');
            console.log(`📦 Playwright version: ${stdout.trim()}`);
            
            // Ejecutar tests de Playwright
            const { stdout: testOutput } = await execAsync('npx playwright test carousel-mobile-test.js --reporter=json');
            
            try {
                const reportePlaywright = JSON.parse(testOutput);
                this.resultados.diagnosticos.playwright = reportePlaywright;
                console.log('✅ Tests Playwright completados');
            } catch (parseError) {
                console.log('⚠️ Playwright ejecutado pero no se pudo parsear el resultado');
                this.resultados.diagnosticos.playwright = { 
                    executed: true, 
                    rawOutput: testOutput 
                };
            }
            
        } catch (error) {
            console.log('⚠️ Playwright no disponible o falló:', error.message);
            this.resultados.diagnosticos.playwright = { 
                error: 'Playwright no disponible',
                message: 'Instalar con: npm run install:playwright'
            };
        }
    }
    
    async generarReporteConsolidado() {
        const reporte = {
            ...this.resultados,
            resumen: this.generarResumen(),
            recomendaciones: this.generarRecomendaciones(),
            siguientesPasos: this.generarSiguientesPasos()
        };
        
        // Guardar reporte principal
        const reportePath = path.join(__dirname, 'reports', 'diagnostico-completo.json');
        fs.writeFileSync(reportePath, JSON.stringify(reporte, null, 2));
        
        // Generar reporte HTML legible
        await this.generarReporteHTML(reporte);
        
        console.log(`📄 Reporte guardado en: ${reportePath}`);
    }
    
    generarResumen() {
        const resumen = {
            problemasDetectados: 0,
            severidadMaxima: 'BAJO',
            dispositivosProbados: 0,
            recomendacionesPrioritarias: []
        };
        
        // Analizar resultados CSS
        if (this.resultados.diagnosticos.css && !this.resultados.diagnosticos.css.error) {
            resumen.problemasDetectados += this.resultados.diagnosticos.css.resumen?.problemasEncontrados || 0;
            if (this.resultados.diagnosticos.css.resumen?.nivelSeveridad === 'ALTO') {
                resumen.severidadMaxima = 'ALTO';
            }
        }
        
        // Analizar resultados Puppeteer
        if (this.resultados.diagnosticos.puppeteer && !this.resultados.diagnosticos.puppeteer.error) {
            resumen.dispositivosProbados = this.resultados.diagnosticos.puppeteer.resumen?.dispositivosProbados || 0;
            resumen.problemasDetectados += this.resultados.diagnosticos.puppeteer.resumen?.problemasDetectados || 0;
        }
        
        return resumen;
    }
    
    generarRecomendaciones() {
        const recomendaciones = [];
        
        // Recomendaciones basadas en CSS
        if (this.resultados.diagnosticos.css?.recomendacionesGenerales) {
            recomendaciones.push(...this.resultados.diagnosticos.css.recomendacionesGenerales);
        }
        
        // Recomendaciones basadas en comportamiento móvil
        if (this.resultados.diagnosticos.puppeteer?.resumen?.problemasDetectados > 0) {
            recomendaciones.push({
                categoria: 'COMPORTAMIENTO_MOVIL',
                titulo: 'Optimizar configuración para móvil',
                descripcion: 'Se detectaron problemas en el comportamiento del carrusel en dispositivos móviles',
                prioridad: 'ALTA',
                acciones: [
                    'Revisar configuración de centeredSlides',
                    'Ajustar slidesPerView para móvil',
                    'Optimizar transiciones iniciales'
                ]
            });
        }
        
        return recomendaciones;
    }
    
    generarSiguientesPasos() {
        return [
            {
                paso: 1,
                titulo: 'Implementar correcciones CSS',
                descripcion: 'Aplicar las correcciones sugeridas en el análisis CSS',
                archivos: ['custom-code/madam-carousel-stepless-custom-code.html']
            },
            {
                paso: 2,
                titulo: 'Probar configuración JavaScript',
                descripcion: 'Ajustar la configuración del carrusel para móvil',
                archivos: ['custom-code/madam-carousel-stepless-custom-code.html']
            },
            {
                paso: 3,
                titulo: 'Validar en dispositivos reales',
                descripcion: 'Probar los cambios en dispositivos móviles reales',
                herramientas: ['Chrome DevTools', 'BrowserStack', 'Dispositivos físicos']
            },
            {
                paso: 4,
                titulo: 'Monitorear rendimiento',
                descripcion: 'Verificar que las optimizaciones no afecten el rendimiento',
                metricas: ['FCP', 'LCP', 'CLS', 'FID']
            }
        ];
    }
    
    async generarReporteHTML(reporte) {
        const html = `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico Carrusel Móvil - Madam Online Store</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; }
        .problema { background: #ffe6e6; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .sugerencia { background: #e6f3ff; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .recomendacion { background: #f0f8e6; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .severidad-alto { border-left-color: #dc3545; }
        .severidad-medio { border-left-color: #ffc107; }
        .severidad-bajo { border-left-color: #28a745; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Diagnóstico Carrusel Móvil</h1>
        <p><strong>Fecha:</strong> ${new Date(reporte.timestamp).toLocaleString('es-ES')}</p>
        <p><strong>Problemas detectados:</strong> ${reporte.resumen.problemasDetectados}</p>
        <p><strong>Severidad máxima:</strong> <span class="severidad-${reporte.resumen.severidadMaxima.toLowerCase()}">${reporte.resumen.severidadMaxima}</span></p>
    </div>
    
    <div class="section">
        <h2>📊 Resumen Ejecutivo</h2>
        <ul>
            <li>Dispositivos probados: ${reporte.resumen.dispositivosProbados}</li>
            <li>Problemas detectados: ${reporte.resumen.problemasDetectados}</li>
            <li>Nivel de severidad: ${reporte.resumen.severidadMaxima}</li>
        </ul>
    </div>
    
    ${reporte.recomendaciones.length > 0 ? `
    <div class="section">
        <h2>💡 Recomendaciones Prioritarias</h2>
        ${reporte.recomendaciones.map(rec => `
            <div class="recomendacion">
                <h3>${rec.titulo}</h3>
                <p><strong>Categoría:</strong> ${rec.categoria}</p>
                <p><strong>Prioridad:</strong> ${rec.prioridad}</p>
                <p>${rec.descripcion}</p>
                ${rec.acciones ? `<ul>${rec.acciones.map(accion => `<li>${accion}</li>`).join('')}</ul>` : ''}
            </div>
        `).join('')}
    </div>
    ` : ''}
    
    <div class="section">
        <h2>🚀 Siguientes Pasos</h2>
        <ol>
            ${reporte.siguientesPasos.map(paso => `
                <li>
                    <strong>${paso.titulo}</strong><br>
                    ${paso.descripcion}
                    ${paso.archivos ? `<br><em>Archivos: ${paso.archivos.join(', ')}</em>` : ''}
                </li>
            `).join('')}
        </ol>
    </div>
    
    <div class="section">
        <h2>📋 Detalles Técnicos</h2>
        <pre>${JSON.stringify(reporte.diagnosticos, null, 2)}</pre>
    </div>
</body>
</html>`;
        
        const htmlPath = path.join(__dirname, 'reports', 'diagnostico-completo.html');
        fs.writeFileSync(htmlPath, html);
        console.log(`📄 Reporte HTML generado: ${htmlPath}`);
    }
    
    mostrarResumen() {
        console.log('\n' + '='.repeat(60));
        console.log('📋 RESUMEN DEL DIAGNÓSTICO');
        console.log('='.repeat(60));
        console.log(`🔍 Problemas detectados: ${this.resultados.diagnosticos.css?.resumen?.problemasEncontrados || 0}`);
        console.log(`📱 Dispositivos probados: ${this.resultados.diagnosticos.puppeteer?.resumen?.dispositivosProbados || 0}`);
        console.log(`⚠️ Severidad máxima: ${this.resultados.diagnosticos.css?.resumen?.nivelSeveridad || 'N/A'}`);
        console.log('\n📄 Reportes generados:');
        console.log('  - diagnostics/reports/diagnostico-completo.json');
        console.log('  - diagnostics/reports/diagnostico-completo.html');
        console.log('\n💡 Revisa los reportes para ver las recomendaciones detalladas.');
    }
}

// Ejecutar diagnóstico si se llama directamente
if (require.main === module) {
    const runner = new DiagnosticRunner();
    runner.ejecutarTodos()
        .then(() => {
            console.log('\n🎉 Diagnóstico completado exitosamente');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Error en el diagnóstico:', error);
            process.exit(1);
        });
}

module.exports = { DiagnosticRunner };
