{"type": "elementor", "siteurl": "https://madam.supply/wp-json/", "elements": [{"id": "2e66f9c", "elType": "widget", "isInner": false, "isLocked": false, "settings": {"carousel_name": "Product Galley Carousel", "__dynamic__": {"carousel": "[elementor-tag id=\"fa16716\" name=\"woocommerce-product-gallery-tag\" settings=\"%7B%7D\"]"}, "thumbnail_size": "full", "slides_to_show": "4", "navigation": "none", "pause_on_hover": "", "speed": 5000, "custom_css": "", "link_to": "file", "open_lightbox": "yes", "pause_on_interaction": "", "autoplay_speed": 3000, "autoplay": "yes", "carousel": [], "thumbnail_custom_dimension": {"width": "", "height": ""}, "slides_to_show_tablet": "", "slides_to_show_mobile": "", "slides_to_scroll": "", "slides_to_scroll_tablet": "", "slides_to_scroll_mobile": "", "image_stretch": "no", "navigation_previous_icon": {"value": "", "library": ""}, "navigation_next_icon": {"value": "", "library": ""}, "link": {"url": "", "is_external": "", "nofollow": "", "custom_attributes": ""}, "caption_type": "", "lazyload": "", "infinite": "yes", "effect": "slide", "direction": "ltr", "arrows_position": "inside", "arrows_size": {"unit": "px", "size": "", "sizes": []}, "arrows_size_tablet": {"unit": "px", "size": "", "sizes": []}, "arrows_size_mobile": {"unit": "px", "size": "", "sizes": []}, "arrows_color": "", "dots_position": "outside", "dots_gap": {"unit": "px", "size": "", "sizes": []}, "dots_gap_tablet": {"unit": "px", "size": "", "sizes": []}, "dots_gap_mobile": {"unit": "px", "size": "", "sizes": []}, "dots_size": {"unit": "px", "size": "", "sizes": []}, "dots_size_tablet": {"unit": "px", "size": "", "sizes": []}, "dots_size_mobile": {"unit": "px", "size": "", "sizes": []}, "dots_inactive_color": "", "dots_color": "", "gallery_vertical_align": "", "gallery_vertical_align_tablet": "", "gallery_vertical_align_mobile": "", "image_spacing": "", "image_spacing_custom": {"unit": "px", "size": 20, "sizes": []}, "image_spacing_custom_tablet": {"unit": "px", "size": "", "sizes": []}, "image_spacing_custom_mobile": {"unit": "px", "size": "", "sizes": []}, "image_border_border": "", "image_border_width": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "image_border_width_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "image_border_width_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "image_border_color": "", "image_border_radius": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "image_border_radius_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "image_border_radius_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "caption_align": "center", "caption_align_tablet": "", "caption_align_mobile": "", "caption_text_color": "", "caption_typography_typography": "", "caption_typography_font_family": "", "caption_typography_font_size": {"unit": "px", "size": "", "sizes": []}, "caption_typography_font_size_tablet": {"unit": "px", "size": "", "sizes": []}, "caption_typography_font_size_mobile": {"unit": "px", "size": "", "sizes": []}, "caption_typography_font_weight": "", "caption_typography_text_transform": "", "caption_typography_font_style": "", "caption_typography_text_decoration": "", "caption_typography_line_height": {"unit": "px", "size": "", "sizes": []}, "caption_typography_line_height_tablet": {"unit": "em", "size": "", "sizes": []}, "caption_typography_line_height_mobile": {"unit": "em", "size": "", "sizes": []}, "caption_typography_letter_spacing": {"unit": "px", "size": "", "sizes": []}, "caption_typography_letter_spacing_tablet": {"unit": "px", "size": "", "sizes": []}, "caption_typography_letter_spacing_mobile": {"unit": "px", "size": "", "sizes": []}, "caption_typography_word_spacing": {"unit": "px", "size": "", "sizes": []}, "caption_typography_word_spacing_tablet": {"unit": "em", "size": "", "sizes": []}, "caption_typography_word_spacing_mobile": {"unit": "em", "size": "", "sizes": []}, "caption_shadow_text_shadow_type": "", "caption_shadow_text_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "color": "rgba(0,0,0,0.3)"}, "caption_space": {"unit": "px", "size": "", "sizes": []}, "caption_space_tablet": {"unit": "px", "size": "", "sizes": []}, "caption_space_mobile": {"unit": "px", "size": "", "sizes": []}, "_title": "", "_margin": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_margin_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_margin_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_element_width": "", "_element_width_tablet": "", "_element_width_mobile": "", "_element_custom_width": {"unit": "%", "size": "", "sizes": []}, "_element_custom_width_tablet": {"unit": "px", "size": "", "sizes": []}, "_element_custom_width_mobile": {"unit": "px", "size": "", "sizes": []}, "_grid_column": "", "_grid_column_tablet": "", "_grid_column_mobile": "", "_grid_column_custom": "", "_grid_column_custom_tablet": "", "_grid_column_custom_mobile": "", "_grid_row": "", "_grid_row_tablet": "", "_grid_row_mobile": "", "_grid_row_custom": "", "_grid_row_custom_tablet": "", "_grid_row_custom_mobile": "", "_flex_align_self": "", "_flex_align_self_tablet": "", "_flex_align_self_mobile": "", "_flex_order": "", "_flex_order_tablet": "", "_flex_order_mobile": "", "_flex_order_custom": "", "_flex_order_custom_tablet": "", "_flex_order_custom_mobile": "", "_flex_size": "", "_flex_size_tablet": "", "_flex_size_mobile": "", "_flex_grow": 1, "_flex_grow_tablet": "", "_flex_grow_mobile": "", "_flex_shrink": 1, "_flex_shrink_tablet": "", "_flex_shrink_mobile": "", "_element_vertical_align": "", "_element_vertical_align_tablet": "", "_element_vertical_align_mobile": "", "_position": "", "_offset_orientation_h": "start", "_offset_x": {"unit": "px", "size": 0, "sizes": []}, "_offset_x_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_x_mobile": {"unit": "px", "size": "", "sizes": []}, "_offset_x_end": {"unit": "px", "size": 0, "sizes": []}, "_offset_x_end_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_x_end_mobile": {"unit": "px", "size": "", "sizes": []}, "_offset_orientation_v": "start", "_offset_y": {"unit": "px", "size": 0, "sizes": []}, "_offset_y_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_y_mobile": {"unit": "px", "size": "", "sizes": []}, "_offset_y_end": {"unit": "px", "size": 0, "sizes": []}, "_offset_y_end_tablet": {"unit": "px", "size": "", "sizes": []}, "_offset_y_end_mobile": {"unit": "px", "size": "", "sizes": []}, "_z_index": "", "_z_index_tablet": "", "_z_index_mobile": "", "_element_id": "", "_css_classes": "stepless-ratio-carousel", "e_display_conditions": "", "_element_cache": "", "motion_fx_motion_fx_scrolling": "", "motion_fx_translateY_effect": "", "motion_fx_translateY_direction": "", "motion_fx_translateY_speed": {"unit": "px", "size": 4, "sizes": []}, "motion_fx_translateY_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "motion_fx_translateX_effect": "", "motion_fx_translateX_direction": "", "motion_fx_translateX_speed": {"unit": "px", "size": 4, "sizes": []}, "motion_fx_translateX_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "motion_fx_opacity_effect": "", "motion_fx_opacity_direction": "out-in", "motion_fx_opacity_level": {"unit": "px", "size": 10, "sizes": []}, "motion_fx_opacity_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "motion_fx_blur_effect": "", "motion_fx_blur_direction": "out-in", "motion_fx_blur_level": {"unit": "px", "size": 7, "sizes": []}, "motion_fx_blur_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "motion_fx_rotateZ_effect": "", "motion_fx_rotateZ_direction": "", "motion_fx_rotateZ_speed": {"unit": "px", "size": 1, "sizes": []}, "motion_fx_rotateZ_affectedRange": {"unit": "%", "size": "", "sizes": {"start": 0, "end": 100}}, "motion_fx_scale_effect": "", "motion_fx_scale_direction": "out-in", "motion_fx_scale_speed": {"unit": "px", "size": 4, "sizes": []}, "motion_fx_scale_range": {"unit": "%", "size": "", "sizes": {"start": 20, "end": 80}}, "motion_fx_transform_origin_x": "center", "motion_fx_transform_origin_y": "center", "motion_fx_devices": ["desktop", "tablet", "mobile"], "motion_fx_range": "", "motion_fx_motion_fx_mouse": "", "motion_fx_mouseTrack_effect": "", "motion_fx_mouseTrack_direction": "", "motion_fx_mouseTrack_speed": {"unit": "px", "size": 1, "sizes": []}, "motion_fx_tilt_effect": "", "motion_fx_tilt_direction": "", "motion_fx_tilt_speed": {"unit": "px", "size": 4, "sizes": []}, "handle_motion_fx_asset_loading": "", "sticky": "", "sticky_on": ["desktop", "tablet", "mobile"], "sticky_offset": 0, "sticky_offset_tablet": "", "sticky_offset_mobile": "", "sticky_effects_offset": 0, "sticky_effects_offset_tablet": "", "sticky_effects_offset_mobile": "", "sticky_anchor_link_offset": 0, "sticky_anchor_link_offset_tablet": "", "sticky_anchor_link_offset_mobile": "", "sticky_parent": "", "_animation": "", "_animation_tablet": "", "_animation_mobile": "", "animation_duration": "", "_animation_delay": "", "_transform_rotate_popover": "", "_transform_rotateZ_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateZ_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateZ_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotate_3d": "", "_transform_rotateX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateX_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateX_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateY_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_perspective_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translate_popover": "", "_transform_translateX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_popover": "", "_transform_keep_proportions": "yes", "_transform_scale_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_skew_popover": "", "_transform_skewX_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_skewX_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewX_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect": {"unit": "px", "size": "", "sizes": []}, "_transform_skewY_effect_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_flipX_effect": "", "_transform_flipY_effect": "", "_transform_rotate_popover_hover": "", "_transform_rotateZ_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateZ_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateZ_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotate_3d_hover": "", "_transform_rotateX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateX_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateX_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_rotateY_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_rotateY_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_perspective_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_perspective_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translate_popover_hover": "", "_transform_translateX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateX_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_translateY_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_popover_hover": "", "_transform_keep_proportions_hover": "yes", "_transform_scale_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scale_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleX_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_hover_tablet": {"unit": "px", "size": "", "sizes": []}, "_transform_scaleY_effect_hover_mobile": {"unit": "px", "size": "", "sizes": []}, "_transform_skew_popover_hover": "", "_transform_skewX_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_skewX_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewX_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect_hover": {"unit": "px", "size": "", "sizes": []}, "_transform_skewY_effect_hover_tablet": {"unit": "deg", "size": "", "sizes": []}, "_transform_skewY_effect_hover_mobile": {"unit": "deg", "size": "", "sizes": []}, "_transform_flipX_effect_hover": "", "_transform_flipY_effect_hover": "", "_transform_transition_hover": {"unit": "px", "size": "", "sizes": []}, "motion_fx_transform_x_anchor_point": "", "motion_fx_transform_x_anchor_point_tablet": "", "motion_fx_transform_x_anchor_point_mobile": "", "motion_fx_transform_y_anchor_point": "", "motion_fx_transform_y_anchor_point_tablet": "", "motion_fx_transform_y_anchor_point_mobile": "", "_background_background": "", "_background_color": "", "_background_color_stop": {"unit": "%", "size": 0, "sizes": []}, "_background_color_stop_tablet": {"unit": "%"}, "_background_color_stop_mobile": {"unit": "%"}, "_background_color_b": "#f2295b", "_background_color_b_stop": {"unit": "%", "size": 100, "sizes": []}, "_background_color_b_stop_tablet": {"unit": "%"}, "_background_color_b_stop_mobile": {"unit": "%"}, "_background_gradient_type": "linear", "_background_gradient_angle": {"unit": "deg", "size": 180, "sizes": []}, "_background_gradient_angle_tablet": {"unit": "deg"}, "_background_gradient_angle_mobile": {"unit": "deg"}, "_background_gradient_position": "center center", "_background_gradient_position_tablet": "", "_background_gradient_position_mobile": "", "_background_image": {"url": "", "id": "", "size": ""}, "_background_image_tablet": {"url": "", "id": "", "size": ""}, "_background_image_mobile": {"url": "", "id": "", "size": ""}, "_background_position": "", "_background_position_tablet": "", "_background_position_mobile": "", "_background_xpos": {"unit": "px", "size": 0, "sizes": []}, "_background_xpos_tablet": {"unit": "px", "size": 0, "sizes": []}, "_background_xpos_mobile": {"unit": "px", "size": 0, "sizes": []}, "_background_ypos": {"unit": "px", "size": 0, "sizes": []}, "_background_ypos_tablet": {"unit": "px", "size": 0, "sizes": []}, "_background_ypos_mobile": {"unit": "px", "size": 0, "sizes": []}, "_background_attachment": "", "_background_repeat": "", "_background_repeat_tablet": "", "_background_repeat_mobile": "", "_background_size": "", "_background_size_tablet": "", "_background_size_mobile": "", "_background_bg_width": {"unit": "%", "size": 100, "sizes": []}, "_background_bg_width_tablet": {"unit": "px", "size": "", "sizes": []}, "_background_bg_width_mobile": {"unit": "px", "size": "", "sizes": []}, "_background_video_link": "", "_background_video_start": "", "_background_video_end": "", "_background_play_once": "", "_background_play_on_mobile": "", "_background_privacy_mode": "", "_background_video_fallback": {"url": "", "id": "", "size": ""}, "_background_slideshow_gallery": [], "_background_slideshow_loop": "yes", "_background_slideshow_slide_duration": 5000, "_background_slideshow_slide_transition": "fade", "_background_slideshow_transition_duration": 500, "_background_slideshow_background_size": "", "_background_slideshow_background_size_tablet": "", "_background_slideshow_background_size_mobile": "", "_background_slideshow_background_position": "", "_background_slideshow_background_position_tablet": "", "_background_slideshow_background_position_mobile": "", "_background_slideshow_lazyload": "", "_background_slideshow_ken_burns": "", "_background_slideshow_ken_burns_zoom_direction": "in", "_background_hover_background": "", "_background_hover_color": "", "_background_hover_color_stop": {"unit": "%", "size": 0, "sizes": []}, "_background_hover_color_stop_tablet": {"unit": "%"}, "_background_hover_color_stop_mobile": {"unit": "%"}, "_background_hover_color_b": "#f2295b", "_background_hover_color_b_stop": {"unit": "%", "size": 100, "sizes": []}, "_background_hover_color_b_stop_tablet": {"unit": "%"}, "_background_hover_color_b_stop_mobile": {"unit": "%"}, "_background_hover_gradient_type": "linear", "_background_hover_gradient_angle": {"unit": "deg", "size": 180, "sizes": []}, "_background_hover_gradient_angle_tablet": {"unit": "deg"}, "_background_hover_gradient_angle_mobile": {"unit": "deg"}, "_background_hover_gradient_position": "center center", "_background_hover_gradient_position_tablet": "", "_background_hover_gradient_position_mobile": "", "_background_hover_image": {"url": "", "id": "", "size": ""}, "_background_hover_image_tablet": {"url": "", "id": "", "size": ""}, "_background_hover_image_mobile": {"url": "", "id": "", "size": ""}, "_background_hover_position": "", "_background_hover_position_tablet": "", "_background_hover_position_mobile": "", "_background_hover_xpos": {"unit": "px", "size": 0, "sizes": []}, "_background_hover_xpos_tablet": {"unit": "px", "size": 0, "sizes": []}, "_background_hover_xpos_mobile": {"unit": "px", "size": 0, "sizes": []}, "_background_hover_ypos": {"unit": "px", "size": 0, "sizes": []}, "_background_hover_ypos_tablet": {"unit": "px", "size": 0, "sizes": []}, "_background_hover_ypos_mobile": {"unit": "px", "size": 0, "sizes": []}, "_background_hover_attachment": "", "_background_hover_repeat": "", "_background_hover_repeat_tablet": "", "_background_hover_repeat_mobile": "", "_background_hover_size": "", "_background_hover_size_tablet": "", "_background_hover_size_mobile": "", "_background_hover_bg_width": {"unit": "%", "size": 100, "sizes": []}, "_background_hover_bg_width_tablet": {"unit": "px", "size": "", "sizes": []}, "_background_hover_bg_width_mobile": {"unit": "px", "size": "", "sizes": []}, "_background_hover_video_link": "", "_background_hover_video_start": "", "_background_hover_video_end": "", "_background_hover_play_once": "", "_background_hover_play_on_mobile": "", "_background_hover_privacy_mode": "", "_background_hover_video_fallback": {"url": "", "id": "", "size": ""}, "_background_hover_slideshow_gallery": [], "_background_hover_slideshow_loop": "yes", "_background_hover_slideshow_slide_duration": 5000, "_background_hover_slideshow_slide_transition": "fade", "_background_hover_slideshow_transition_duration": 500, "_background_hover_slideshow_background_size": "", "_background_hover_slideshow_background_size_tablet": "", "_background_hover_slideshow_background_size_mobile": "", "_background_hover_slideshow_background_position": "", "_background_hover_slideshow_background_position_tablet": "", "_background_hover_slideshow_background_position_mobile": "", "_background_hover_slideshow_lazyload": "", "_background_hover_slideshow_ken_burns": "", "_background_hover_slideshow_ken_burns_zoom_direction": "in", "_background_hover_transition": {"unit": "px", "size": "", "sizes": []}, "_border_border": "", "_border_width": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_width_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_width_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_color": "", "_border_radius": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_radius_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_radius_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_box_shadow_box_shadow_type": "", "_box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(0,0,0,0.5)"}, "_box_shadow_box_shadow_position": " ", "_border_hover_border": "", "_border_hover_width": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_hover_width_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_hover_width_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_hover_color": "", "_border_radius_hover": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_radius_hover_tablet": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_border_radius_hover_mobile": {"unit": "px", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_box_shadow_hover_box_shadow_type": "", "_box_shadow_hover_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(0,0,0,0.5)"}, "_box_shadow_hover_box_shadow_position": " ", "_border_hover_transition": {"unit": "px", "size": "", "sizes": []}, "_mask_switch": "", "_mask_shape": "circle", "_mask_image": {"url": "", "id": "", "size": ""}, "_mask_notice": "", "_mask_size": "contain", "_mask_size_tablet": "", "_mask_size_mobile": "", "_mask_size_scale": {"unit": "%", "size": 100, "sizes": []}, "_mask_size_scale_tablet": {"unit": "px", "size": "", "sizes": []}, "_mask_size_scale_mobile": {"unit": "px", "size": "", "sizes": []}, "_mask_position": "center center", "_mask_position_tablet": "", "_mask_position_mobile": "", "_mask_position_x": {"unit": "%", "size": 0, "sizes": []}, "_mask_position_x_tablet": {"unit": "px", "size": "", "sizes": []}, "_mask_position_x_mobile": {"unit": "px", "size": "", "sizes": []}, "_mask_position_y": {"unit": "%", "size": 0, "sizes": []}, "_mask_position_y_tablet": {"unit": "px", "size": "", "sizes": []}, "_mask_position_y_mobile": {"unit": "px", "size": "", "sizes": []}, "_mask_repeat": "no-repeat", "_mask_repeat_tablet": "", "_mask_repeat_mobile": "", "hide_desktop": "", "hide_tablet": "", "hide_mobile": "", "_attributes": ""}, "defaultEditSettings": {"defaultEditRoute": "content"}, "elements": [], "widgetType": "image-carousel", "editSettings": {"defaultEditRoute": "content", "panel": {"activeTab": "advanced", "activeSection": "_section_style"}}, "htmlCache": "\t\t\t\t\t<div class=\"elementor-image-carousel-wrapper swiper\" role=\"region\" aria-roledescription=\"carousel\" aria-label=\"Product Galley Carousel\" dir=\"ltr\">\n\t\t\t<div class=\"elementor-image-carousel swiper-wrapper\" aria-live=\"off\">\n\t\t\t\t\t\t\t\t<div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"1 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzIyLCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTIyLmF2aWYiLCJzbGlkZXNob3ciOiIyZTY2ZjljIn0%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-22.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-22.avif\" alt=\"madam-eloa-crimson-22\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"2 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzIxLCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTIxLmF2aWYiLCJzbGlkZXNob3ciOiIyZTY2ZjljIn0%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-21.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-21.avif\" alt=\"madam-eloa-crimson-21\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"3 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzIwLCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTIwLmF2aWYiLCJzbGlkZXNob3ciOiIyZTY2ZjljIn0%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-20.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-20.avif\" alt=\"madam-eloa-crimson-20\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"4 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzE5LCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTE2LmF2aWYiLCJzbGlkZXNob3ciOiIyZTY2ZjljIn0%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-16.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-16.avif\" alt=\"madam-eloa-crimson-16\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"5 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzE4LCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTE1LmF2aWYiLCJzbGlkZXNob3ciOiIyZTY2ZjljIn0%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-15.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-15.avif\" alt=\"madam-eloa-crimson-15\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"6 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzE3LCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTEyLmF2aWYiLCJzbGlkZXNob3ciOiIyZTY2ZjljIn0%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-12.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-12.avif\" alt=\"madam-eloa-crimson-12\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"7 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzE2LCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTkuYXZpZiIsInNsaWRlc2hvdyI6IjJlNjZmOWMifQ%3D%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-9.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-9.avif\" alt=\"madam-eloa-crimson-9\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"8 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzE1LCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTguYXZpZiIsInNsaWRlc2hvdyI6IjJlNjZmOWMifQ%3D%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-8.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-8.avif\" alt=\"Bolso Eloa en cuero color burdeos con asa arqueada sobre fondo neutro\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"9 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzE0LCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTUuYXZpZiIsInNsaWRlc2hvdyI6IjJlNjZmOWMifQ%3D%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-5.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-5.avif\" alt=\"madam-eloa-crimson-5\" /></figure></a></div><div class=\"swiper-slide\" role=\"group\" aria-roledescription=\"slide\" aria-label=\"10 of 10\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"2e66f9c\" data-e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6MzEzLCJ1cmwiOiJodHRwczpcL1wvbWFkYW0uc3VwcGx5XC93cC1jb250ZW50XC91cGxvYWRzXC8yMDI1XC8wNlwvbWFkYW0tZWxvYS1jcmltc29uLTEuYXZpZiIsInNsaWRlc2hvdyI6IjJlNjZmOWMifQ%3D%3D\" class=\"elementor-clickable\" href=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-1.avif\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"https://madam.supply/wp-content/uploads/2025/06/madam-eloa-crimson-1.avif\" alt=\"madam-eloa-crimson-1\" /></figure></a></div>\t\t\t</div>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t"}]}