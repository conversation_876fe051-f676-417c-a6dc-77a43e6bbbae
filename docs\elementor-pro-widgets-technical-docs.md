# Documentación Técnica: Widgets de Elementor Pro con Swiper.js y FlexSlider

## Índice
1. [Arquitectura General](#arquitectura-general)
2. [Widgets que usan Swiper.js](#widgets-que-usan-swiperjs)
3. [Widgets WooCommerce con FlexSlider](#widgets-woocommerce-con-flexslider)
4. [Estructura de Archivos](#estructura-de-archivos)
5. [Handlers JavaScript](#handlers-javascript)
6. [Hooks y Filtros](#hooks-y-filtros)
7. [APIs para Desarrolladores](#apis-para-desarrolladores)

---

## Arquitectura General

### Migración de Bibliotecas

Elementor Pro realizó una migración importante en la versión 2.7.0:
- **Anterior**: Slick Slider
- **Actual**: Swiper.js (desde v2.7.0)
- **<PERSON><PERSON><PERSON>**: <PERSON><PERSON><PERSON> <PERSON>, mayor flexibilid<PERSON>, soporte táctil superior

### Carga Optimizada de Assets (v3.1.0+)

```javascript
// Sistema de carga condicional
if (elementorFrontend.config.experimentalFeatures.improved_asset_loading) {
    // Swiper se carga solo cuando es necesario
    const asyncSwiper = elementorFrontend.utils.swiper;
}
```

---

## Widgets que usan Swiper.js

### 1. Image Carousel Widget

#### Estructura PHP
```php
// Archivo: widgets/image-carousel.php
class Widget_Image_Carousel extends Widget_Base {
    
    public function get_name() {
        return 'image-carousel';
    }
    
    protected function register_controls() {
        // Controles de configuración del carousel
        $this->start_controls_section(
            'section_image_carousel',
            [
                'label' => __('Image Carousel', 'elementor-pro'),
            ]
        );
        
        // Configuraciones específicas de Swiper
        $this->add_control(
            'slides_to_show',
            [
                'label' => __('Slides to Show', 'elementor-pro'),
                'type' => Controls_Manager::RESPONSIVE_SELECT,
                'options' => [
                    '1' => '1',
                    '2' => '2',
                    '3' => '3',
                    '4' => '4',
                    '5' => '5',
                    '6' => '6',
                ],
                'frontend_available' => true,
            ]
        );
    }
    
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        $this->add_render_attribute('carousel', [
            'class' => ['elementor-image-carousel-wrapper', 'swiper-container'],
            'data-widget-id' => $this->get_id(),
        ]);
        
        echo '<div ' . $this->get_render_attribute_string('carousel') . '>';
        echo '<div class="swiper-wrapper">';
        // Renderizado de slides
        echo '</div>';
        echo '</div>';
    }
}
```

#### Handler JavaScript
```javascript
// Archivo: assets/js/widgets/image-carousel.js
class ImageCarouselHandler extends elementorModules.frontend.handlers.SwiperBase {
    
    getDefaultSettings() {
        return {
            selectors: {
                carousel: '.elementor-image-carousel-wrapper',
                slideContent: '.swiper-slide',
            },
        };
    }
    
    getDefaultElements() {
        const selectors = this.getSettings('selectors');
        const elements = {
            $swiperContainer: this.$element.find(selectors.carousel),
        };
        
        elements.$slides = elements.$swiperContainer.find(selectors.slideContent);
        
        return elements;
    }
    
    getSwiperSettings() {
        const elementSettings = this.getElementSettings();
        
        const swiperOptions = {
            grabCursor: true,
            initialSlide: this.getInitialSlide(),
            slidesPerView: this.getSlidesCount(),
            slidesPerGroup: this.getSlidesCount(),
            spaceBetween: this.getSpaceBetween(),
            loop: 'yes' === elementSettings.infinite,
            speed: elementSettings.transition_speed,
            handleElementorBreakpoints: true,
        };
        
        if (elementSettings.autoplay) {
            swiperOptions.autoplay = {
                delay: elementSettings.autoplay_speed,
                disableOnInteraction: 'yes' === elementSettings.pause_on_interaction,
            };
        }
        
        if (elementSettings.show_arrows) {
            swiperOptions.navigation = {
                prevEl: '.elementor-swiper-button-prev',
                nextEl: '.elementor-swiper-button-next',
            };
        }
        
        if (elementSettings.pagination) {
            swiperOptions.pagination = {
                el: '.swiper-pagination',
                type: elementSettings.pagination,
                clickable: true,
            };
        }
        
        return swiperOptions;
    }
    
    updateSlidesPerView() {
        const elementSettings = this.getElementSettings();
        const deviceMode = elementorFrontend.getCurrentDeviceMode();
        const slidesToShow = elementSettings[`slides_to_show_${deviceMode}`] || elementSettings.slides_to_show;
        
        if (this.swiperInstance) {
            this.swiperInstance.params.slidesPerView = slidesToShow;
            this.swiperInstance.update();
        }
    }
    
    onElementChange(propertyName) {
        if (propertyName.includes('slides_to_show')) {
            this.updateSlidesPerView();
        }
        
        super.onElementChange(propertyName);
    }
}
```

---

## Hooks y Filtros

### Hooks PHP para Widgets

#### Filtros de Configuración
```php
// Filtro para modificar configuración de Swiper
add_filter('elementor_pro/widgets/carousel/swiper_config', function($config, $widget_instance) {
    // Modificar configuración específica
    if ('image-carousel' === $widget_instance->get_name()) {
        $config['effect'] = 'coverflow';
        $config['coverflowEffect'] = [
            'rotate' => 50,
            'stretch' => 0,
            'depth' => 100,
            'modifier' => 1,
            'slideShadows' => true,
        ];
    }
    
    return $config;
}, 10, 2);

// Filtro para assets de Swiper
add_filter('elementor_pro/frontend/swiper/needs_swiper', function($needs_swiper, $widget_type) {
    $swiper_widgets = [
        'image-carousel',
        'media-carousel',
        'testimonial-carousel',
        'reviews',
    ];
    
    return in_array($widget_type, $swiper_widgets);
}, 10, 2);
```

#### Actions para Inicialización
```php
// Action antes de renderizar carousel
do_action('elementor_pro/widgets/carousel/before_render', $this, $settings);

// Action después de renderizar carousel
do_action('elementor_pro/widgets/carousel/after_render', $this, $settings);

// Action para cargar assets específicos
do_action('elementor_pro/frontend/after_register_scripts');
```

### Hooks JavaScript

#### Frontend Hooks
```javascript
// Hook antes de inicializar Swiper
elementorFrontend.hooks.addAction('frontend/element_ready/global', function($scope) {
    console.log('Elemento listo:', $scope);
});

// Hook específico para carousels
elementorFrontend.hooks.addAction('frontend/element_ready/image-carousel.default', function($scope) {
    // Personalización específica para image carousel
    const $carousel = $scope.find('.elementor-image-carousel-wrapper');
    
    if ($carousel.length) {
        // Configuración personalizada
        $carousel.on('swiperInitialized', function(e, swiper) {
            console.log('Swiper inicializado:', swiper);
        });
    }
});

// Hook para cuando Swiper está listo
elementorFrontend.hooks.addAction('frontend/swiper_init', function(swiperInstance, $element) {
    // Configuración global para todas las instancias de Swiper
    console.log('Nueva instancia de Swiper:', swiperInstance);
});
```

#### Custom Events
```javascript
// Evento personalizado cuando se cambia de slide
class CustomCarouselHandler extends SwiperBase {
    
    getSwiperSettings() {
        const config = super.getSwiperSettings();
        
        config.on = {
            slideChange: (swiper) => {
                // Disparar evento personalizado
                this.$element.trigger('elementor:carousel:slideChange', [swiper, swiper.activeIndex]);
            },
            
            reachEnd: (swiper) => {
                this.$element.trigger('elementor:carousel:reachEnd', [swiper]);
            },
            
            reachBeginning: (swiper) => {
                this.$element.trigger('elementor:carousel:reachBeginning', [swiper]);
            }
        };
        
        return config;
    }
}

// Escuchar eventos personalizados
jQuery(document).on('elementor:carousel:slideChange', function(e, swiper, activeIndex) {
    console.log('Slide cambió al índice:', activeIndex);
});
```

---

## APIs para Desarrolladores

### API de Swiper en Elementor

#### Acceso a Instancias
```javascript
// Método 1: Usando data attribute
const swiperInstance = jQuery('.elementor-image-carousel-wrapper').data('swiper');

// Método 2: Usando el handler de Elementor
const elementorElement = jQuery('.elementor-element-[id]');
const handler = elementorElement.data('elementorHandler');
const swiper = handler.swiperInstance;

// Método 3: Usando el registry global
const swiper = elementorFrontend.utils.swiper.getInstanceById('carousel-id');
```

#### Control Programático
```javascript
// Navegación manual
swiper.slideNext();     // Siguiente slide
swiper.slidePrev();     // Slide anterior
swiper.slideTo(2);      // Ir al slide específico

// Control de autoplay
swiper.autoplay.start();
swiper.autoplay.stop();

// Actualización dinámica
swiper.update();        // Actualizar dimensiones
swiper.updateSlides();  // Actualizar slides

// Destruir instancia
swiper.destroy(true, true);
```

#### Eventos Disponibles
```javascript
swiper.on('slideChange', function() {
    console.log('Slide actual:', this.activeIndex);
});

swiper.on('touchStart', function() {
    console.log('Inicio de touch');
});

swiper.on('touchEnd', function() {
    console.log('Fin de touch');
});

swiper.on('transitionStart', function() {
    console.log('Inicio de transición');
});

swiper.on('transitionEnd', function() {
    console.log('Fin de transición');
});
```

### API de FlexSlider en WooCommerce

#### Configuración Global
```php
// Filtro para configuración de FlexSlider
add_filter('woocommerce_single_product_carousel_options', function($options) {
    return array_merge($options, [
        'animation' => 'slide',
        'animationSpeed' => 400,
        'slideshow' => false,
        'controlNav' => 'thumbnails',
        'directionNav' => true,
        'prevText' => '<i class="fa fa-angle-left"></i>',
        'nextText' => '<i class="fa fa-angle-right"></i>',
        'rtl' => is_rtl(),
        
        // Callbacks personalizados
        'start' => 'function(slider) { 
            console.log("FlexSlider iniciado"); 
        }',
        'before' => 'function(slider) { 
            console.log("Antes del slide"); 
        }',
        'after' => 'function(slider) { 
            console.log("Después del slide"); 
        }',
    ]);
});
```

#### Control JavaScript
```javascript
// Acceso a la instancia de FlexSlider
const flexslider = jQuery('.woocommerce-product-gallery').data('flexslider');

// Métodos de control
flexslider.play();      // Iniciar slideshow
flexslider.pause();     // Pausar slideshow
flexslider.stop();      // Detener slideshow
flexslider.next();      // Siguiente slide
flexslider.prev();      // Slide anterior
flexslider.flexslider(2); // Ir al slide 3 (índice 2)

// Agregar/remover slides dinámicamente
flexslider.addSlide('<li><img src="nueva-imagen.jpg"></li>');
flexslider.removeSlide(0); // Remover primer slide
```

---

## Optimización y Rendimiento

### Carga Condicional de Assets

#### JavaScript Optimizado
```php
// Archivo: includes/frontend.php
class Frontend {
    
    public function enqueue_scripts() {
        // Carga condicional de Swiper
        if ($this->is_swiper_needed()) {
            wp_enqueue_script(
                'swiper',
                ELEMENTOR_PRO_ASSETS_URL . 'lib/swiper/swiper.min.js',
                [],
                ELEMENTOR_PRO_VERSION,
                true
            );
        }
        
        // Carga condicional de FlexSlider
        if ($this->is_flexslider_needed()) {
            wp_enqueue_script(
                'flexslider',
                ELEMENTOR_PRO_ASSETS_URL . 'lib/flexslider/jquery.flexslider.js',
                ['jquery'],
                ELEMENTOR_PRO_VERSION,
                true
            );
        }
    }
    
    private function is_swiper_needed() {
        global $post;
        
        if (!$post) {
            return false;
        }
        
        // Verificar si hay widgets que usan Swiper
        $elementor_data = get_post_meta($post->ID, '_elementor_data', true);
        
        if (empty($elementor_data)) {
            return false;
        }
        
        $swiper_widgets = [
            'image-carousel',
            'media-carousel',
            'testimonial-carousel',
            'reviews',
        ];
        
        return $this->has_widgets($elementor_data, $swiper_widgets);
    }
    
    private function has_widgets($data, $widget_types) {
        foreach ($data as $element) {
            if (isset($element['widgetType']) && in_array($element['widgetType'], $widget_types)) {
                return true;
            }
            
            if (isset($element['elements']) && $this->has_widgets($element['elements'], $widget_types)) {
                return true;
            }
        }
        
        return false;
    }
}
```

### Lazy Loading para Imágenes
```javascript
// Implementación de lazy loading en carousels
class LazyLoadCarousel extends SwiperBase {
    
    getSwiperSettings() {
        const config = super.getSwiperSettings();
        
        config.lazy = {
            loadPrevNext: true,
            loadPrevNextAmount: 2,
            loadOnTransitionStart: false,
        };
        
        config.on = {
            ...config.on,
            lazyImageLoad: (swiper, slideEl, imageEl) => {
                console.log('Imagen cargada:', imageEl.src);
            },
            lazyImageReady: (swiper, slideEl, imageEl) => {
                console.log('Imagen lista:', imageEl.src);
            }
        };
        
        return config;
    }
    
    prepareSlides() {
        this.elements.$slides.each((index, slide) => {
            const $slide = jQuery(slide);
            const $img = $slide.find('img');
            
            if ($img.length) {
                const src = $img.attr('src');
                $img.attr('data-src', src);
                $img.addClass('swiper-lazy');
                $img.attr('src', '');
                
                // Agregar loader
                $slide.append('<div class="swiper-lazy-preloader"></div>');
            }
        });
    }
    
    onInit() {
        this.prepareSlides();
        super.onInit();
    }
}
```

### Responsive Breakpoints Dinámicos
```javascript
class ResponsiveCarousel extends SwiperBase {
    
    getResponsiveBreakpoints() {
        const elementSettings = this.getElementSettings();
        const activeBreakpoints = elementorFrontend.config.kit.active_breakpoints;
        
        const breakpoints = {};
        
        activeBreakpoints.forEach(breakpoint => {
            const breakpointConfig = elementorFrontend.config.kit.breakpoints[breakpoint];
            
            breakpoints[breakpointConfig.value] = {
                slidesPerView: elementSettings[`slides_to_show_${breakpoint}`] || 1,
                spaceBetween: elementSettings[`space_between_${breakpoint}`] || 10,
            };
        });
        
        return breakpoints;
    }
    
    getSwiperSettings() {
        const config = super.getSwiperSettings();
        
        config.breakpoints = this.getResponsiveBreakpoints();
        
        return config;
    }
}
```

---

## Debugging y Troubleshooting

### Debug Mode para Carousels
```javascript
class DebugCarousel extends SwiperBase {
    
    constructor(...args) {
        super(...args);
        this.debugMode = elementorFrontend.config.environmentMode.edit;
    }
    
    log(...args) {
        if (this.debugMode) {
            console.log('[Elementor Carousel Debug]', ...args);
        }
    }
    
    getSwiperSettings() {
        const config = super.getSwiperSettings();
        
        if (this.debugMode) {
            config.on = {
                ...config.on,
                init: (swiper) => {
                    this.log('Swiper inicializado:', swiper.params);
                },
                slideChange: (swiper) => {
                    this.log('Slide cambió:', {
                        activeIndex: swiper.activeIndex,
                        previousIndex: swiper.previousIndex,
                        slides: swiper.slides.length
                    });
                },
                breakpoint: (swiper, breakpointParams) => {
                    this.log('Breakpoint activado:', breakpointParams);
                }
            };
        }
        
        return config;
    }
    
    validateConfiguration() {
        const settings = this.getElementSettings();
        const issues = [];
        
        if (!settings.slides_to_show || settings.slides_to_show < 1) {
            issues.push('slides_to_show debe ser mayor a 0');
        }
        
        if (settings.autoplay && (!settings.autoplay_speed || settings.autoplay_speed < 100)) {
            issues.push('autoplay_speed debe ser al menos 100ms');
        }
        
        if (issues.length > 0) {
            this.log('Problemas de configuración encontrados:', issues);
        }
        
        return issues.length === 0;
    }
    
    async onInit() {
        if (!this.validateConfiguration()) {
            return;
        }
        
        await super.onInit();
        
        this.log('Carousel inicializado correctamente');
    }
}
```

### Herramientas de Diagnóstico
```javascript
// Utilidad para diagnosticar problemas con carousels
window.ElementorCarouselDiagnostic = {
    
    getSwiperInstances() {
        const instances = [];
        
        jQuery('.swiper-container').each(function() {
            const $this = jQuery(this);
            const swiper = $this.data('swiper');
            
            if (swiper) {
                instances.push({
                    element: this,
                    $element: $this,
                    swiper: swiper,
                    params: swiper.params,
                    slides: swiper.slides.length,
                    activeIndex: swiper.activeIndex
                });
            }
        });
        
        return instances;
    },
    
    getFlexSliderInstances() {
        const instances = [];
        
        jQuery('.flexslider').each(function() {
            const $this = jQuery(this);
            const flexslider = $this.data('flexslider');
            
            if (flexslider) {
                instances.push({
                    element: this,
                    $element: $this,
                    flexslider: flexslider,
                    vars: flexslider.vars,
                    count: flexslider.count,
                    currentSlide: flexslider.currentSlide
                });
            }
        });
        
        return instances;
    },
    
    reportIssues() {
        const report = {
            swiper: {
                instances: this.getSwiperInstances(),
                issues: []
            },
            flexslider: {
                instances: this.getFlexSliderInstances(),
                issues: []
            }
        };
        
        // Verificar problemas comunes
        report.swiper.instances.forEach(instance => {
            if (instance.slides === 0) {
                report.swiper.issues.push('Swiper sin slides detectado');
            }
            
            if (!instance.swiper.initialized) {
                report.swiper.issues.push('Swiper no inicializado correctamente');
            }
        });
        
        console.table(report);
        return report;
    }
};

// Comando de consola para diagnóstico rápido
console.log('Ejecuta ElementorCarouselDiagnostic.reportIssues() para diagnosticar problemas');
```

Esta documentación técnica completa proporciona toda la información necesaria para desarrolladores que trabajan con widgets de Elementor Pro que utilizan Swiper.js y FlexSlider, incluyendo estructura de archivos, handlers JavaScript, hooks, filtros, APIs y herramientas de debugging.
    }
    
    async onInit() {
        super.onInit();
        
        if (!this.elements.$swiperContainer.length) {
            return;
        }
        
        const Swiper = elementorFrontend.utils.swiper;
        this.swiper = await new Swiper(this.elements.$swiperContainer, this.getSwiperSettings());
        
        // Exponer la instancia
        this.elements.$swiperContainer.data('swiper', this.swiper);
    }
}

// Registro del handler
jQuery(window).on('elementor/frontend/init', () => {
    const addHandler = ($element) => {
        elementorFrontend.elementsHandler.addHandler(ImageCarouselHandler, {
            $element,
        });
    };
    
    elementorFrontend.hooks.addAction('frontend/element_ready/image-carousel.default', addHandler);
});
```

### 2. Media Carousel Widget

#### Configuración PHP
```php
// Archivo: widgets/media-carousel.php
class Widget_Media_Carousel extends Widget_Base {
    
    protected function register_controls() {
        // Controles específicos para medios
        $this->add_control(
            'skin',
            [
                'label' => __('Skin', 'elementor-pro'),
                'type' => Controls_Manager::SELECT,
                'default' => 'carousel',
                'options' => [
                    'carousel' => __('Carousel', 'elementor-pro'),
                    'slideshow' => __('Slideshow', 'elementor-pro'),
                    'coverflow' => __('Coverflow', 'elementor-pro'),
                ],
                'frontend_available' => true,
            ]
        );
        
        $this->add_control(
            'effect',
            [
                'label' => __('Effect', 'elementor-pro'),
                'type' => Controls_Manager::SELECT,
                'default' => 'slide',
                'options' => [
                    'slide' => __('Slide', 'elementor-pro'),
                    'fade' => __('Fade', 'elementor-pro'),
                    'cube' => __('Cube', 'elementor-pro'),
                    'coverflow' => __('Coverflow', 'elementor-pro'),
                    'flip' => __('Flip', 'elementor-pro'),
                ],
                'frontend_available' => true,
            ]
        );
    }
}
```

#### Handler JavaScript Avanzado
```javascript
// Archivo: assets/js/widgets/media-carousel.js
class MediaCarouselHandler extends elementorModules.frontend.handlers.SwiperBase {
    
    getSwiperSettings() {
        const elementSettings = this.getElementSettings();
        const slidesCount = this.getSlidesCount();
        
        const swiperOptions = {
            initialSlide: this.getInitialSlide(),
            grabCursor: true,
            effect: elementSettings.effect,
            preventClicksPropagation: false,
            slideToClickedSlide: true,
            handleElementorBreakpoints: true,
        };
        
        // Configuración específica por efecto
        switch (elementSettings.effect) {
            case 'fade':
                swiperOptions.fadeEffect = { crossFade: true };
                break;
            case 'cube':
                swiperOptions.cubeEffect = {
                    shadow: true,
                    slideShadows: true,
                };
                break;
            case 'coverflow':
                swiperOptions.coverflowEffect = {
                    rotate: 50,
                    stretch: 0,
                    depth: 100,
                    modifier: 1,
                    slideShadows: true,
                };
                break;
        }
        
        // Configuración responsiva
        if (slidesCount < elementSettings.slides_to_show) {
            swiperOptions.slidesPerView = slidesCount;
        } else {
            swiperOptions.slidesPerView = elementSettings.slides_to_show;
            swiperOptions.slidesPerGroup = elementSettings.slides_to_scroll;
        }
        
        if (elementSettings.image_spacing_custom) {
            swiperOptions.spaceBetween = elementSettings.image_spacing_custom.size;
        }
        
        // Controles de navegación
        if (elementSettings.navigation) {
            swiperOptions.navigation = {
                prevEl: '.elementor-swiper-button-prev',
                nextEl: '.elementor-swiper-button-next',
            };
        }
        
        if (elementSettings.pagination) {
            swiperOptions.pagination = {
                el: '.swiper-pagination',
                type: elementSettings.pagination,
                clickable: true,
            };
        }
        
        // Lightbox integration
        if (elementSettings.open_lightbox) {
            this.handleLightboxIntegration();
        }
        
        return swiperOptions;
    }
    
    handleLightboxIntegration() {
        const lightboxOptions = {
            type: 'image',
            modalOptions: {
                id: 'elementor-lightbox-' + this.getID(),
            },
        };
        
        this.elements.$slides.each((index, slide) => {
            const $slide = jQuery(slide);
            const $image = $slide.find('img');
            
            $image.on('click', (e) => {
                e.preventDefault();
                
                const items = this.getLightboxItems();
                
                elementorFrontend.utils.lightbox.openSlideshow(items, index, lightboxOptions);
            });
        });
    }
    
    getLightboxItems() {
        const items = [];
        
        this.elements.$slides.each((index, slide) => {
            const $slide = jQuery(slide);
            const $image = $slide.find('img');
            const item = {
                image: $image.attr('src'),
                index,
                title: $image.attr('alt'),
            };
            
            items.push(item);
        });
        
        return items;
    }
}
```

### 3. Testimonial Carousel Widget

#### Estructura PHP
```php
// Archivo: widgets/testimonial-carousel.php
class Widget_Testimonial_Carousel extends Widget_Base {
    
    protected function register_controls() {
        $this->start_controls_section(
            'section_slides',
            [
                'label' => __('Slides', 'elementor-pro'),
            ]
        );
        
        $repeater = new Repeater();
        
        $repeater->add_control(
            'content',
            [
                'label' => __('Content', 'elementor-pro'),
                'type' => Controls_Manager::TEXTAREA,
                'default' => __('Lorem ipsum dolor sit amet, consectetur adipiscing elit.', 'elementor-pro'),
                'label_block' => true,
                'rows' => '10',
            ]
        );
        
        $repeater->add_control(
            'image',
            [
                'label' => __('Avatar', 'elementor-pro'),
                'type' => Controls_Manager::MEDIA,
                'skins' => ['classic'],
            ]
        );
        
        $this->add_control(
            'slides',
            [
                'label' => __('Slides', 'elementor-pro'),
                'type' => Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                'default' => [
                    [
                        'content' => __('Lorem ipsum dolor sit amet, consectetur adipiscing elit.', 'elementor-pro'),
                        'name' => __('John Doe', 'elementor-pro'),
                        'title' => __('CEO', 'elementor-pro'),
                    ],
                ],
                'title_field' => '{{{ name }}}',
            ]
        );
    }
    
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        if (empty($settings['slides'])) {
            return;
        }
        
        $this->add_render_attribute('wrapper', [
            'class' => ['elementor-testimonial-wrapper', 'elementor-slick-slider'],
        ]);
        
        $this->add_render_attribute('carousel', [
            'class' => 'elementor-testimonial-carousel swiper-container',
        ]);
        
        ?>
        <div <?php echo $this->get_render_attribute_string('wrapper'); ?>>
            <div <?php echo $this->get_render_attribute_string('carousel'); ?>>
                <div class="swiper-wrapper">
                    <?php foreach ($settings['slides'] as $slide) : ?>
                        <div class="swiper-slide">
                            <div class="elementor-testimonial">
                                <?php if (!empty($slide['content'])) : ?>
                                    <div class="elementor-testimonial__content">
                                        <?php echo $slide['content']; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="elementor-testimonial__footer">
                                    <?php if (!empty($slide['image']['url'])) : ?>
                                        <div class="elementor-testimonial__image">
                                            <img src="<?php echo esc_url($slide['image']['url']); ?>" alt="<?php echo esc_attr($slide['name']); ?>">
                                        </div>
                                    <?php endif; ?>
                                    
                                    <cite class="elementor-testimonial__cite">
                                        <span class="elementor-testimonial__name"><?php echo $slide['name']; ?></span>
                                        <span class="elementor-testimonial__title"><?php echo $slide['title']; ?></span>
                                    </cite>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php
    }
}
```

---

## Widgets WooCommerce con FlexSlider

### 1. WooCommerce Products Widget

#### Estructura PHP
```php
// Archivo: widgets/woocommerce/products.php
class Widget_Woocommerce_Products extends Widget_Base {
    
    protected function register_controls() {
        parent::register_controls();
        
        $this->start_controls_section(
            'section_design_carousel',
            [
                'label' => __('Carousel', 'elementor-pro'),
                'tab' => Controls_Manager::TAB_STYLE,
                'condition' => [
                    'carousel_enabled' => 'yes',
                ],
            ]
        );
        
        $this->add_control(
            'carousel_enabled',
            [
                'label' => __('Enable Carousel', 'elementor-pro'),
                'type' => Controls_Manager::SWITCHER,
                'frontend_available' => true,
            ]
        );
        
        $this->add_control(
            'slides_to_show',
            [
                'label' => __('Slides to Show', 'elementor-pro'),
                'type' => Controls_Manager::RESPONSIVE_NUMBER,
                'condition' => [
                    'carousel_enabled' => 'yes',
                ],
                'frontend_available' => true,
            ]
        );
    }
    
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        if ('yes' === $settings['carousel_enabled']) {
            $this->add_render_attribute('products', [
                'class' => 'elementor-products-carousel',
                'data-carousel' => wp_json_encode([
                    'slides_to_show' => $settings['slides_to_show'],
                    'slides_to_scroll' => $settings['slides_to_scroll'],
                    'autoplay' => 'yes' === $settings['autoplay'],
                    'autoplay_speed' => $settings['autoplay_speed'],
                ]),
            ]);
        }
        
        // Renderizado de productos WooCommerce
        parent::render();
    }
}
```

#### Handler JavaScript para Productos
```javascript
// Archivo: assets/js/widgets/woocommerce-products.js
class WooCommerceProductsHandler extends elementorModules.frontend.handlers.Base {
    
    getDefaultSettings() {
        return {
            selectors: {
                productsContainer: '.elementor-products-carousel',
                productItem: '.product',
            },
        };
    }
    
    getDefaultElements() {
        const selectors = this.getSettings('selectors');
        
        return {
            $productsContainer: this.$element.find(selectors.productsContainer),
            $products: this.$element.find(selectors.productItem),
        };
    }
    
    onInit() {
        super.onInit();
        
        if (!this.elements.$productsContainer.length) {
            return;
        }
        
        this.initCarousel();
    }
    
    async initCarousel() {
        const $container = this.elements.$productsContainer;
        const carouselData = $container.data('carousel');
        
        if (!carouselData) {
            return;
        }
        
        // Convertir estructura para Swiper
        $container.addClass('swiper-container');
        $container.wrapInner('<div class="swiper-wrapper"></div>');
        this.elements.$products.addClass('swiper-slide');
        
        // Configuración de Swiper
        const swiperConfig = {
            slidesPerView: carouselData.slides_to_show || 4,
            slidesPerGroup: carouselData.slides_to_scroll || 1,
            spaceBetween: 30,
            loop: true,
            breakpoints: {
                320: { slidesPerView: 1 },
                768: { slidesPerView: 2 },
                1024: { slidesPerView: carouselData.slides_to_show || 4 },
            },
        };
        
        if (carouselData.autoplay) {
            swiperConfig.autoplay = {
                delay: carouselData.autoplay_speed || 3000,
            };
        }
        
        // Inicializar Swiper
        const Swiper = elementorFrontend.utils.swiper;
        this.swiper = await new Swiper($container[0], swiperConfig);
    }
}

// Registro del handler
jQuery(window).on('elementor/frontend/init', () => {
    elementorFrontend.hooks.addAction('frontend/element_ready/wc-products.default', ($element) => {
        elementorFrontend.elementsHandler.addHandler(WooCommerceProductsHandler, {
            $element,
        });
    });
});
```

### 2. Product Gallery Widget (FlexSlider)

#### Implementación FlexSlider
```javascript
// Archivo: assets/js/widgets/woocommerce-product-gallery.js
class WooCommerceProductGalleryHandler extends elementorModules.frontend.handlers.Base {
    
    onInit() {
        super.onInit();
        this.initProductGallery();
    }
    
    initProductGallery() {
        const $gallery = this.$element.find('.woocommerce-product-gallery');
        
        if (!$gallery.length || typeof $.fn.flexslider === 'undefined') {
            return;
        }
        
        const flexsliderConfig = {
            selector: '.woocommerce-product-gallery__wrapper > .woocommerce-product-gallery__image',
            animation: 'slide',
            animationSpeed: 500,
            animationLoop: false,
            slideshow: false,
            controlNav: 'thumbnails',
            directionNav: true,
            prevText: '',
            nextText: '',
            rtl: elementorFrontend.config.is_rtl,
        };
        
        // Aplicar configuración personalizada
        const customConfig = this.getElementSettings('gallery_options');
        if (customConfig) {
            Object.assign(flexsliderConfig, customConfig);
        }
        
        $gallery.flexslider(flexsliderConfig);
        
        // Exponer instancia para acceso externo
        $gallery.data('flexslider-instance', $gallery.data('flexslider'));
    }
    
    getElementSettings(setting) {
        return elementorFrontend.getCurrentDeviceSetting(
            this.getElementSettingsKey(),
            setting
        );
    }
}
```

---

## Estructura de Archivos

### Archivos Core de Elementor Pro

```
elementor-pro/
├── assets/
│   ├── js/
│   │   ├── frontend.js                    // Handler principal
│   │   ├── frontend.min.js               // Versión minificada
│   │   ├── elements-handlers.js          // Handlers de widgets
│   │   └── widgets/
│   │       ├── image-carousel.js
│   │       ├── media-carousel.js
│   │       ├── testimonial-carousel.js
│   │       └── woocommerce/
│   │           ├── products.js
│   │           └── single-product.js
│   ├── css/
│   │   ├── frontend.css
│   │   └── frontend.min.css
│   └── lib/
│       ├── swiper/
│       │   ├── swiper.min.js
│       │   └── swiper.min.css
│       └── flexslider/
│           ├── jquery.flexslider.js
│           └── flexslider.css
├── modules/
│   ├── carousel/
│   │   ├── module.php
│   │   └── widgets/
│   │       ├── image-carousel.php
│   │       └── media-carousel.php
│   └── woocommerce/
│       ├── module.php
│       └── widgets/
│           ├── products.php
│           └── single-product.php
└── includes/
    ├── widgets-manager.php
    └── base/
        └── widget-base.php
```

### Archivos de Configuración

#### Frontend Configuration
```javascript
// Configuración global disponible en elementorFrontend.config
{
    "urls": {
        "assets": "https://domain.com/wp-content/plugins/elementor-pro/assets/"
    },
    "settings": {
        "page": [],
        "editorPreferences": []
    },
    "kit": {
        "active_breakpoints": ["viewport_mobile", "viewport_tablet"],
        "global_image_lightbox": "yes",
        "lightbox_enable_counter": "yes",
        "lightbox_enable_fullscreen": "yes",
        "lightbox_enable_zoom": "yes",
        "lightbox_enable_share": "yes"
    },
    "post": {
        "id": 123,
        "title": "Page Title",
        "excerpt": ""
    },
    "user": {
        "roles": ["administrator"]
    },
    "swiperLibrarySource": "min"
}
```

---

## Handlers JavaScript

### Base Handler para Swiper
```javascript
// Archivo: assets/js/base/swiper-base.js
class SwiperBase extends elementorModules.frontend.handlers.Base {
    
    constructor(...args) {
        super(...args);
        this.swiperInstance = null;
    }
    
    bindEvents() {
        this.elements.$swiperContainer.on('keydown', this.onKeyDown.bind(this));
    }
    
    unbindEvents() {
        this.elements.$swiperContainer.off('keydown', this.onKeyDown.bind(this));
    }
    
    onKeyDown(event) {
        const keyCodes = {
            37: 'prev', // Left
            39: 'next', // Right
        };
        
        if (keyCodes[event.keyCode]) {
            this.swiperInstance[`slide${keyCodes[event.keyCode].charAt(0).toUpperCase() + keyCodes[event.keyCode].slice(1)}`]();
        }
    }
    
    getSlidesCount() {
        return this.elements.$slides.length;
    }
    
    getInitialSlide() {
        const editSettings = this.getEditSettings();
        return editSettings.activeItemIndex || 0;
    }
    
    getSpaceBetween() {
        const propertyName = 'space_between';
        return elementorFrontend.utils.controls.getResponsiveControlValue(
            this.getElementSettings(),
            propertyName,
            '',
            this.getCurrentDeviceMode()
        );
    }
    
    getSwiperSettings() {
        // Debe ser implementado por las clases hijas
        return {};
    }
    
    async onInit() {
        super.onInit();
        
        const Swiper = elementorFrontend.utils.swiper;
        this.swiperInstance = await new Swiper(
            this.elements.$swiperContainer,
            this.getSwiperSettings()
        );
        
        // Exponer instancia
        this.elements.$swiperContainer.data('swiper', this.swiperInstance);
    }
    
    onElementChange(propertyName) {
        if (0 === propertyName.indexOf('width')) {
            this.swiperInstance.update();
        }
    }
    
    onEditSettingsChange(propertyName) {
        if ('activeItemIndex' === propertyName) {
            this.swiperInstance.slideToLoop(this.getEditSettings('activeItemIndex') || 0);
        }
    }
}
```

### Handler Específico de Image Carousel
```javascript
class ImageCarouselHandler extends SwiperBase {
    
    getDefaultSettings() {
        return {
            selectors: {
                carousel: '.elementor-image-carousel-wrapper',
                slideContent: '.swiper-slide',
                slideInnerContent: '.swiper-slide-inner',
            },
        };
    }
    
    getDefaultElements() {
        const selectors = this.getSettings('selectors');
        const elements = {
            $swiperContainer: this.$element.find(selectors.carousel),
        };
        
        elements.$slides = elements.$swiperContainer.find(selectors.slideContent);
        
        return elements;
    }
    
    getSwiperSettings() {
        const elementSettings = this.getElementSettings();
        
        const swiperOptions = {
            grabCursor: true,
            initialSlide: this.getInitialSlide(),
            slidesPerView: this.getSlidesCount(),
            slidesPerGroup: this.getSlidesCount(),
            spaceBetween: this.getSpaceBetween(),
            loop: 'yes' === elementSettings.infinite,
            speed: elementSettings.transition_speed,
            handleElementorBreakpoints: true,
        };
        
        // Configuración de autoplay
        if (elementSettings.autoplay) {
            swiperOptions.autoplay = {
                delay: elementSettings.autoplay_speed,
                disableOnInteraction: !!elementSettings.pause_on_interaction,
            };
        }
        
        // Navegación
        if (elementSettings.show_arrows) {
            swiperOptions.navigation = {
                prevEl: '.elementor-swiper-button-prev',
                nextEl: '.elementor-swiper-button-next',
            };
        }
        
        // Paginación
        if (elementSettings.pagination) {
            swiperOptions.pagination = {
                el: '.swiper-pagination',
                type: elementSettings.pagination,
                clickable: true,
            };
        }
        
        // Efectos especiales
        if ('fade' === elementSettings.effect) {
            swiperOptions.effect = 'fade';
            swiperOptions.fadeEffect = { crossFade: true };
        }
        
        return swiperOptions;